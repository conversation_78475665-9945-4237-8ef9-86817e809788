using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
using EPOOutline;
using VIC.Core;
using Cysharp.Threading.Tasks;

namespace VIC.Road
{
    
    public class TrafficLight : MonoBehaviour
    {
        public PlayMakerFSM makerFSM;
        public Outlinable[] outlinables;
        //ֻ�����ֽ�ͨ����� ���� 
        public bool TrafficType = false;
        //��ͨ����� ������ ������� �˳����� �˳�����
        public bool TrafficLightType = false;
        public Vector3 Pos;
        public bool isNeedOutline = false;// �ж��Ƿ�����˵�� ����˾Ͳ�ȡ���Ƴ������ĸ���

        // 控制是否使用自动计算红灯时间
        public bool useAutoCalculation = true;
        // 自定义红灯时间（当不使用自动计算时）
        public int customRedTime = 30;

        #region Unity�������ں���
        private void Awake()
        {
            TrafficLightController.InitTrafficLight(this.gameObject);
            InitOutlinables(false);
            Vector3 temp = transform.parent.transform.position;
            temp.y = 4;
            Pos = temp;
            this.gameObject.SetActive(false);
        }
        

        private void OnMouseEnter()
        {      
            if(!TrafficLightController.Instance.isLight)
            InitOutlinables(true);
        }
        public void OnMouseDown()
        {
            if (!TrafficLightController.Instance.isLight)
            {
                isNeedOutline = true;
                TrafficLightController.Instance.isLight = true;
                TrafficLightController.Instance.InitCurTrafficLight(this);
                EventCenter.Instance.EventTrigger(EventName.ShowTimeChangePanel, true);
                var temp = GetPlayMakerAction();
                if (TrafficType)
                    EventCenter.Instance.EventTrigger(EventName.InitTimeData, temp[1]);
                else
                    EventCenter.Instance.EventTrigger(EventName.InitTimeData, temp[0]);
            }
        }
        private void OnMouseExit()
        {
            //�˴����ܳ��� �����ڵ����� ���ܳɹ�ȡ������
            if(!isNeedOutline)
            InitOutlinables(false);
        }
        #endregion
        public TimeData[] GetPlayMakerAction()
        {
            TimeData[] temps = new TimeData[2];
            var actions = makerFSM.FsmStates;

            // 读取红灯时间
            foreach (var action in actions)
            {
                if (action.Name == "All RED" || action.Name == "All RED ")
                {
                    GetFsmActionWait(ref temps[0], 2, action);
                    GetFsmActionWait(ref temps[1], 2, action);
                    break; // 找到红灯状态后就退出循环
                }
            }

            foreach (var action in actions)
            {
                if (action.Name == "WAIT 1")
                {
                        GetFsmActionWait(ref temps[0],0, action);
                }
                else if(action.Name == "YELLOW 1"||action.Name== "YELLOW 2")
                {
                        GetFsmActionWait(ref temps[0],1, action);
                }
            }
                foreach (var action in actions)
                {
                    if (action.Name == "WAIT 2")
                    {
                        GetFsmActionWait(ref temps[1],0, action);
                    }
                    else if (action.Name == "YELLOW 3" || action.Name == "YELLOW 4")
                    {
                        if (action.Actions[action.Actions.Length - 1] is
                        HutongGames.PlayMaker.Actions.Wait)
                        {
                            GetFsmActionWait(ref temps[1],1, action);
                        }
                    }
                }
                //17 �����е� ͬ��
            // 根据useAutoCalculation决定是否使用自动计算红灯时间
            if (useAutoCalculation)
            {
                temps[0].RedTime = temps[1].GreTime + temps[1].YelTime * 2;
                temps[1].RedTime = temps[0].GreTime + temps[0].YelTime * 2;
                if (TrafficLightType)
                {
                    temps[0].RedTime += 6;
                    temps[1].RedTime += 6;
                }
                else
                {
                    temps[0].RedTime += 20;
                    temps[1].RedTime += 20;
                }
            }
            else
            {
                // 使用自定义红灯时间
                temps[0].RedTime = customRedTime;
                temps[1].RedTime = customRedTime;
            }
            return temps;
        }
        public void SetPlayMakerAction(TimeData data)
        {
            var actions = makerFSM.FsmStates;
            foreach (var action in actions)
            {
                // 设置红灯时间
                if (action.Name == "All RED" || action.Name == "All RED ")
                {
                    if (GetFsmActionWait(action) != null)
                        GetFsmActionWait(action).time = data.RedTime;
                }
            }

            if (!TrafficType)
            {
                foreach (var action in actions)
                {
                    if (action.Name == "WAIT 1")
                    {
                        if (GetFsmActionWait(action) != null)
                            GetFsmActionWait(action).time = data.GreTime;
                    }
                    else if (action.Name == "YELLOW 1" || action.Name == "YELLOW 2")
                    {
                        if (GetFsmActionWait(action)!=null)
                        GetFsmActionWait(action).time= data.YelTime;
                    }
                }
            }
            else
            {
                foreach (var action in actions)
                {
                    if (action.Name == "WAIT 2")
                    {
                        if (GetFsmActionWait(action) != null)
                            GetFsmActionWait(action).time = data.GreTime;
                    }
                    else if (action.Name == "YELLOW 3" || action.Name == "YELLOW 4")
                    {
                        if (GetFsmActionWait(action) != null)
                            GetFsmActionWait(action).time = data.YelTime;
                    }
                }
            }
            // 只有在使用自动计算模式时才重新获取计算后的时间数据
            // 否则直接使用传入的data，避免覆盖用户的手动设置
            TimeData temp;
            if (useAutoCalculation)
            {
                if(!TrafficType)
                {
                     temp = GetPlayMakerAction()[0];
                }
                else
                {
                     temp = GetPlayMakerAction()[1];
                }
                EventCenter.Instance.EventTrigger(EventName.InitTimeData, temp);
            }
            else
            {
                // 手动模式下，直接使用传入的data，不重新计算
                EventCenter.Instance.EventTrigger(EventName.InitTimeData, data);
            }
        }
        //�õ�WAit aciton ���Ҹ�ֵ��TimeData
        private static void GetFsmActionWait(ref TimeData temp,int i, HutongGames.PlayMaker.FsmState action)
        {
            if (action.Actions[action.Actions.Length - 1] is HutongGames.PlayMaker.Actions.Wait)
            {
                if(i==0)
                    temp.GreTime = ((action.Actions[action.Actions.Length - 1] as
                       HutongGames.PlayMaker.Actions.Wait).time).ToInt();
                else if(i==1)
                    temp.YelTime = ((action.Actions[action.Actions.Length - 1] as
                       HutongGames.PlayMaker.Actions.Wait).time).ToInt();
                else if(i==2)
                    temp.RedTime = ((action.Actions[action.Actions.Length - 1] as
                       HutongGames.PlayMaker.Actions.Wait).time).ToInt();
            }
        }
        //�õ�������Wait Action
        private HutongGames.PlayMaker.Actions.Wait GetFsmActionWait(HutongGames.PlayMaker.FsmState action)
        {
            if (action.Actions[action.Actions.Length - 1] is HutongGames.PlayMaker.Actions.Wait)
                return (action.Actions[action.Actions.Length - 1]) as HutongGames.PlayMaker.Actions.Wait;
            else return null;
        }

        //���Ƹ���
        public void InitOutlinables(bool isActive)
        {
            for (int i = 0; i < outlinables.Length; i++)
            {
                outlinables[i].enabled = isActive;
            }
        }
        private void OnDestroy()
        {
            TrafficLightController.RemoveTrafficLight(this.gameObject);
        }
    }
}


