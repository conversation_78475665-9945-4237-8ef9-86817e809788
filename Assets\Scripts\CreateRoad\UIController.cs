using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.UI;
using VIC.Core;
using VIC.Scene;
namespace VIC.Road
{
    public class UIController : MonoBehaviour
    {
        public RoadSystemController systemController;
        [Header("���")]
        public Button compeleteButton;
        public Button comfirmButton;
        public Button exitButton;
        public RectTransform comfirmPanel;
        public LocalNavMeshBuilder builder;

        public GameObject AI_Cars;
        public GameObject AI_Deliverys;
        public GameObject AI_Peds;
        public GameObject[] hideObjs;
        public Toggle[] toggles;
        [Header("���")]
        public GameObject DataPanel;
        void Awake()
        {
            comfirmPanel.gameObject.SetActive(false);
            AI_Cars.SetActive(false);
            AI_Peds.SetActive(false);
            AI_Deliverys.SetActive(false);

            compeleteButton.onClick.AddListener(() =>
            {
                comfirmPanel.gameObject.SetActive(true);
            });
            comfirmButton.onClick.AddListener(() =>
            {
                builder.DoBake();
                comfirmPanel.gameObject.SetActive(false);
                AI_Cars.SetActive(true);
                AI_Peds.SetActive(true);
                AI_Deliverys.SetActive(true);
                DataPanel.SetActive(true);
                EventCenter.Instance.EventTrigger(EventName.SetTrafficLight, true);
                for (int i = 0; i < hideObjs.Length; i++)
                {
                    hideObjs[i].SetActive(false);
                }
                for (int i = 0; i < NewRoadCommand.roadBuilddingClicks.Count; i++)
                {

                    Destroy(NewRoadCommand.roadBuilddingClicks[i]);
                    Destroy(NewRoadCommand.roadBuiddingDrags[i]);
                }
            });
            exitButton.onClick.AddListener(() =>
            {
                SceneMgr.RetrunLastScene();

            });
            toggles[0].onValueChanged.AddListener((V) =>
            {
                systemController.RemoveRoadIcon();
                systemController.CreateRoadIcon(RoadType.Normal);
            });
            toggles[1].onValueChanged.AddListener((V) =>
            {
                systemController.RemoveRoadIcon();
                systemController.CreateRoadIcon(RoadType.WithTranficLight);
            });
            toggles[2].onValueChanged.AddListener((V) =>
            {
                systemController.RemoveRoadIcon();
                systemController.CreateRoadIcon(RoadType.Trun);
            });
        }
    }

}
