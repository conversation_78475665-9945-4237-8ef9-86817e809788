using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;
using VIC.Core;

namespace VIC.Road
{
    public struct TimeData
    {
        public int RedTime;
        public int YelTime;
        public int GreTime;
    }
    public class TimeChangePanel : MonoBehaviour
    {
        [Header("���")]
        public TMPro.TextMeshProUGUI[] texts;
        public InputField[] fields;
        public Button comfirmBtn;
        public Button closeBtn;
        [Header("自动计算控制")]
        public UnityEngine.UI.Toggle autoCalculationToggle; // 控制是否自动计算红灯时间
        private TimeData data;
        private void Awake()
        {
            fields[0].onValueChanged.AddListener((value) => {
                int.TryParse(value, out data.RedTime);
                // 同时更新当前红绿灯的自定义红灯时间
                var currentLight = TrafficLightController.Instance.curLight;
                if (currentLight != null)
                {
                    currentLight.customRedTime = data.RedTime;
                }
            });
            fields[1].onValueChanged.AddListener((value) => { int.TryParse(value, out data.YelTime); });
            fields[2].onValueChanged.AddListener((value) => { int.TryParse(value, out data.GreTime); });

            // 添加自动计算开关的事件监听
            if (autoCalculationToggle != null)
            {
                autoCalculationToggle.onValueChanged.AddListener((isOn) => {
                    var currentLight = TrafficLightController.Instance.curLight;
                    if (currentLight != null)
                    {
                        currentLight.useAutoCalculation = isOn;

                        if (isOn)
                        {
                            // 切换到自动计算模式：重新计算并更新显示
                            var temp = currentLight.GetPlayMakerAction();
                            if (currentLight.TrafficType)
                                EventCenter.Instance.EventTrigger(EventName.InitTimeData, temp[1]);
                            else
                                EventCenter.Instance.EventTrigger(EventName.InitTimeData, temp[0]);
                        }
                        else
                        {
                            // 切换到手动模式：保持当前输入框的值，只更新customRedTime
                            int redTime;
                            if (int.TryParse(fields[0].text, out redTime))
                            {
                                currentLight.customRedTime = redTime;
                                data.RedTime = redTime;
                            }
                        }
                    }
                });
            }

            comfirmBtn.onClick.AddListener(OnBtnClick);
            closeBtn.onClick.AddListener(() => {
                EventCenter.Instance.EventTrigger(EventName.SetCurTrafficOutline, false);
                this.gameObject.SetActive(false); });
            EventCenter.Instance.AddEventListener<bool>(EventName.ShowTimeChangePanel, ShowPanel);
            EventCenter.Instance.AddEventListener<TimeData>(EventName.InitTimeData, InitTimeData);
            this.gameObject.SetActive(false);
        }
        private void OnBtnClick()
        {
            EventCenter.Instance.EventTrigger(EventName.SetPlayMakerAtionData, data);
            EventCenter.Instance.EventTrigger(EventName.SetCurTrafficOutline, false);
            this.gameObject.SetActive(false);
        }
        public void ShowPanel(bool isActive)
        {
            this.gameObject.SetActive(isActive);
        }
        public void InitTimeData(TimeData timeData)
        {
            // texts[0].text = $"{timeData.RedTime}";
            fields[0].text = timeData.RedTime.ToString();
            fields[1].text = timeData.YelTime.ToString();
            fields[2].text = timeData.GreTime.ToString();

            // 设置Toggle状态以反映当前红绿灯的自动计算设置
            var currentLight = TrafficLightController.Instance.curLight;
            if (currentLight != null && autoCalculationToggle != null)
            {
                autoCalculationToggle.isOn = currentLight.useAutoCalculation;
            }

            // 更新data结构
            data.RedTime = timeData.RedTime;
            data.YelTime = timeData.YelTime;
            data.GreTime = timeData.GreTime;
        }
        private void Update()
        {

        }
    }

}

