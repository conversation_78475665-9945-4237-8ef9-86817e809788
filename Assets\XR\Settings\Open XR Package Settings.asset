%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-7447064140843925751
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: feeef8d85de8db242bdda70cc7ff5acd, type: 3}
  m_Name: OculusTouchControllerProfile Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Oculus Touch Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.oculustouch
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-7233210105174236505
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 274c02963f889a64e90bc2e596e21d13, type: 3}
  m_Name: HTCViveControllerProfile Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: HTC Vive Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.htcvive
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-6541352154418117643
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 486b5e28864f9a94b979b9620ce5006d, type: 3}
  m_Name: ConformanceAutomationFeature Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Conformance Automation
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.conformance
  openxrExtensionStrings: XR_EXT_conformance_automation
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-6218958968486852088
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 056125dd64c0ed540b40a4af74f7b495, type: 3}
  m_Name: RuntimeDebuggerOpenXRFeature Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Runtime Debugger
  version: 1
  featureIdInternal: com.unity.openxr.features.runtimedebugger
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
  cacheSize: 1048576
  perThreadCacheSize: 51200
--- !u!114 &-6112611874982703959
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 761fdd4502cb7a84e9ec7a2b24f33f37, type: 3}
  m_Name: MicrosoftMotionControllerProfile Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Microsoft Motion Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.microsoftmotioncontroller
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-3230559108194028456
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0d6ccd3d0ef0f1d458e69421dccbdae1, type: 3}
  m_Name: ValveIndexControllerProfile Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Valve Index Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.valveindex
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-2880327786826459695
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0f6bfdbcb316ed242b30a8798c9eb853, type: 3}
  m_Name: KHRSimpleControllerProfile Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Khronos Simple Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.khrsimpleprofile
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-423142004022035189
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f928d0d73a35f294fbe357ca17aa3547, type: 3}
  m_Name: MicrosoftHandInteraction Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Microsoft Hand Interaction Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.handtracking
  openxrExtensionStrings: XR_MSFT_hand_interaction
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-40941369210103281
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b3cf79659a011bd419c7a2a30eb74e9a, type: 3}
  m_Name: EyeGazeInteraction Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Eye Gaze Interaction Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.eyetracking
  openxrExtensionStrings: XR_EXT_eye_gaze_interaction
  company: Unity
  priority: 0
  required: 0
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9f0ebc320a151d3408ea1e9fce54d40e, type: 3}
  m_Name: Open XR Package Settings
  m_EditorClassIdentifier: 
  Keys: 01000000
  Values:
  - {fileID: 7290246136508287702}
--- !u!114 &704187652882423145
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7de993716e042c6499d0c18eed3a773c, type: 3}
  m_Name: MockRuntime Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Mock Runtime
  version: 0.0.2
  featureIdInternal: com.unity.openxr.feature.mockruntime
  openxrExtensionStrings: XR_UNITY_null_gfx XR_UNITY_android_present
  company: Unity
  priority: 0
  required: 0
  ignoreValidationErrors: 0
--- !u!114 &5147580669360045473
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c4b862ee14fb479fbfe5fffe655d3ed3, type: 3}
  m_Name: MetaQuestTouchProControllerProfile Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Meta Quest Touch Pro Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.metaquestpro
  openxrExtensionStrings: XR_FB_touch_controller_pro
  company: Unity
  priority: 0
  required: 0
--- !u!114 &7290246136508287702
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b5a1f07dc5afe854f9f12a4194aca3fb, type: 3}
  m_Name: Standalone
  m_EditorClassIdentifier: 
  features:
  - {fileID: -6541352154418117643}
  - {fileID: -40941369210103281}
  - {fileID: -7233210105174236505}
  - {fileID: -2880327786826459695}
  - {fileID: 5147580669360045473}
  - {fileID: -423142004022035189}
  - {fileID: -6112611874982703959}
  - {fileID: 704187652882423145}
  - {fileID: -7447064140843925751}
  - {fileID: -6218958968486852088}
  - {fileID: -3230559108194028456}
  m_renderMode: 1
  m_depthSubmissionMode: 0
