using UnityEngine;
using UnityEngine.UI;
using HutongGames.PlayMaker;
using System.Collections;
using System.Collections.Generic;

namespace VIC.Road
{
    /// <summary>
    /// 数据面板控制器 - 简化版本
    ///
    /// 职责：
    /// - UI刷新：显示当前车辆和行人数量
    /// - 简单删除：删除单个车辆或行人
    /// - 按钮响应：CreateCarBtn和CreatePedBtn统一处理添加和删除
    /// </summary>
    public class DataPanel : MonoBehaviour
    {

        #region UI组件
        [Header("UI组件")]
        public Text PedText;              // 行人数量显示
        public Text CarText;              // 车辆数量显示
        public Text DeliveryText;       // 配送车辆数量显示
        public InputField PedInputField;  // 行人数量输入框
        public InputField CarInputField;  // 车辆数量输入框
        public InputField DeliveryInputField;  // 配送车辆数量输入框
        public Button CreatePedBtn;       // 创建/删除行人按钮
        public Button CreateCarBtn;       // 创建/删除车辆按钮
        public Button CreateDeliveryBtn;       // 创建/删除配送车辆按钮

        public Button AddPedBtn;
        public Button DelRedBtn;
        public Button AddCarBtn;
        public Button DelCarBtn;
        public Button AddDeliveryBtn;
        public Button DelDeliveryBtn;

        [Header("摄像机切换")]
        public Button PedCam;
        public Button CarCam;
        public Button DeliveryCam;

        [Header("AI容器")]
        public GameObject AI_CAR;
        public GameObject AI_DELIVERY;
        public GameObject AI_PED;
        #endregion

        private int pendingCarCreations = 0; // 待处理的车辆创建数量
        private int pendingPedCreations = 0; // 待处理的行人创建数量
        private int pendingDeliveryCreations = 0; // 待处理的配送车辆创建数量

        private void Awake()
        {

            // AddCarBtn 和 AddPedBtn 的功能由 CreateCarBtn 和 CreatePedBtn 处理
            DelCarBtn.onClick.AddListener(DeleteSingleCar);
            DelRedBtn.onClick.AddListener(DeleteSinglePed);
            DelDeliveryBtn.onClick.AddListener(DeleteSingleDelivery);

            // 绑定CreatePedBtn到SetCreatePedBtn方法
            CreatePedBtn.onClick.AddListener(SetCreatePedBtn);

            // 绑定CreateCarBtn到SetCreateCarBtn方法
            CreateCarBtn.onClick.AddListener(SetCreateCarBtn);
            CreateDeliveryBtn.onClick.AddListener(SetCreateDeliveryBtn);
        }
        private void Start()
        {
            Refresh();
            gameObject.SetActive(false);
        }
        public void Refresh()
        {
            PedText.text = AI_PED.transform.childCount.ToString();
            CarText.text = AI_CAR.transform.childCount.ToString();
            DeliveryText.text = AI_DELIVERY.transform.childCount.ToString();
            CarCam.interactable = AI_CAR.transform.childCount > 0;
            PedCam.interactable = AI_PED.transform.childCount > 0;
            DeliveryCam.interactable = AI_DELIVERY.transform.childCount > 0;
        }

        private void SetCreatePedBtn()
        {
            // 获取输入框中的数字
            if (int.TryParse(PedInputField.text, out int targetPedCount))
            {
                // 确保目标数量不为负数
                targetPedCount = Mathf.Max(0, targetPedCount);

                // 使用实际的子对象数量而不是计数器变量
                int currentPedCount = AI_PED.transform.childCount;

                if (targetPedCount > currentPedCount)
                {
                    // 需要增加行人，执行 (targetPedCount - currentPedCount) 次 AddPedBtn.onClick
                    int addCount = targetPedCount - currentPedCount;
                    pendingPedCreations = addCount;

                    // 先创建所有行人对象
                    for (int i = 0; i < addCount; i++)
                    {
                        AddPedBtn.onClick.Invoke();
                    }

                    // 开始第一个行人的FSM流程
                    if (pendingPedCreations > 0)
                    {
                        TriggerPedFSMWaitForInput();
                    }
                }
                else if (targetPedCount < currentPedCount)
                {
                    // 需要减少行人，批量删除 (currentPedCount - targetPedCount) 个行人
                    int deleteCount = currentPedCount - targetPedCount;
                    Debug.Log($"需要删除 {deleteCount} 个行人");
                    DeleteMultiplePeds(deleteCount);
                }
                // 如果相等则不需要做任何操作
            }
            else
            {
                Debug.LogWarning("PedInputField 输入的不是有效数字");
            }
        }

        private void SetCreateCarBtn()
        {
            // 获取输入框中的数字
            if (int.TryParse(CarInputField.text, out int targetCarCount))
            {
                // 确保目标数量不为负数
                targetCarCount = Mathf.Max(0, targetCarCount);

                // 使用实际的子对象数量而不是计数器变量
                int currentCarCount = AI_CAR.transform.childCount;

                if (targetCarCount > currentCarCount)
                {
                    // 需要增加车辆，使用队列方式逐个处理
                    int addCount = targetCarCount - currentCarCount;
                    pendingCarCreations = addCount;
                    Debug.Log($"需要创建 {addCount} 个车辆");

                    // 先创建所有车辆对象
                    for (int i = 0; i < addCount; i++)
                    {
                        AddCarBtn.onClick.Invoke();
                    }

                    // 开始第一个车辆的FSM流程
                    if (pendingCarCreations > 0)
                    {
                        TriggerCarFSMWaitForInput();
                    }
                }
                else if (targetCarCount < currentCarCount)
                {
                    // 需要减少车辆，批量删除 (currentCarCount - targetCarCount) 个车辆
                    int deleteCount = currentCarCount - targetCarCount;
                    Debug.Log($"需要删除 {deleteCount} 个车辆");
                    DeleteMultipleCars(deleteCount);
                }
                // 如果相等则不需要做任何操作
            }
            else
            {
                Debug.LogWarning("CarInputField 输入的不是有效数字");
            }
        }

        private void TriggerCarFSMWaitForInput()
        {
            // 获取AI_CAR对象上的PlayMakerFSM组件
            PlayMakerFSM carFSM = AI_CAR.GetComponent<PlayMakerFSM>();
            if (carFSM != null)
            {
                // 发送FINISHED事件到FSM，触发Wait for input状态
                carFSM.SendEvent("FINISHED");
                // Debug.Log("触发AI_CAR FSM的FINISHED事件");
            }
            else
            {
                Debug.LogWarning("AI_CAR对象上未找到PlayMakerFSM组件");
            }
        }


        private void SetCreateDeliveryBtn()
        {
            // 获取输入框中的数字
            if (int.TryParse(DeliveryInputField.text, out int targetDeliveryCount))
            {
                // 确保目标数量不为负数
                targetDeliveryCount = Mathf.Max(0, targetDeliveryCount);

                // 使用实际的子对象数量而不是计数器变量
                int currentDeliveryCount = AI_DELIVERY.transform.childCount;

                if (targetDeliveryCount > currentDeliveryCount)
                {
                    // 需要增加车辆，使用队列方式逐个处理
                    int addCount = targetDeliveryCount - currentDeliveryCount;
                    pendingDeliveryCreations = addCount;
                    Debug.Log($"需要创建 {addCount} 个车辆");

                    // 先创建所有车辆对象
                    for (int i = 0; i < addCount; i++)
                    {
                        AddDeliveryBtn.onClick.Invoke();
                    }

                    // 开始第一个车辆的FSM流程
                    if (pendingDeliveryCreations > 0)
                    {
                        TriggerDeliveryFSMWaitForInput();
                    }
                }
                else if (targetDeliveryCount < currentDeliveryCount)
                {
                    // 需要减少车辆，批量删除 (currentDeliveryCount - targetDeliveryCount) 个车辆
                    int deleteCount = currentDeliveryCount - targetDeliveryCount;
                    Debug.Log($"需要删除 {deleteCount} 个车辆");
                    DeleteMultipleDeliverys(deleteCount);
                }
                // 如果相等则不需要做任何操作
            }
            else
            {
                Debug.LogWarning("DeliveryInputField 输入的不是有效数字");
            }
        }

        private void TriggerDeliveryFSMWaitForInput()
        {
            // 获取AI_DELIVERY对象上的PlayMakerFSM组件
            PlayMakerFSM deliveryFSM = AI_DELIVERY.GetComponent<PlayMakerFSM>();
            if (deliveryFSM != null)
            {
                // 发送FINISHED事件到FSM，触发Wait for input状态
                deliveryFSM.SendEvent("FINISHED");
                // Debug.Log("触发AI_DELIVERY FSM的FINISHED事件");
            }
            else
            {
                Debug.LogWarning("AI_DELIVERY对象上未找到PlayMakerFSM组件");
            }
        }

        // PlayMaker Call Method Action调用此方法 - 车辆创建完成
        public void ReceiveCarCreationCompleteEvent()
        {
            Debug.Log("=== 车辆创建完成，剩余待处理: " + (pendingCarCreations - 1) + " ===");
            pendingCarCreations--;
            Refresh();


            // 如果还有待处理的车辆，延迟触发下一个（避免时序冲突）
            if (pendingCarCreations > 0)
            {
                // Debug.Log("等待0.2秒后触发下一个车辆的FSM流程...");
                StartCoroutine(TriggerCarFSMWithDelay());
            }
            else
            {
                Debug.Log("所有车辆创建流程完成！");
                pendingCarCreations = 0; // 重置计数器
            }
        }

        public void ReceiveDeliveryCreationCompleteEvent()
        {
            Debug.Log("=== 车辆创建完成，剩余待处理: " + (pendingDeliveryCreations - 1) + " ===");
            pendingDeliveryCreations--;
            Refresh();


            // 如果还有待处理的车辆，延迟触发下一个（避免时序冲突）
            if (pendingDeliveryCreations > 0)
            {
                // Debug.Log("等待0.2秒后触发下一个车辆的FSM流程...");
                StartCoroutine(TriggerDeliveryFSMWithDelay());
            }
            else
            {
                Debug.Log("所有车辆创建流程完成！");
                pendingDeliveryCreations = 0; // 重置计数器
            }
        }

        // PlayMaker Call Method Action调用此方法 - 行人创建完成
        public void ReceivePedCreationCompleteEvent()
        {
            Debug.Log("=== 行人创建完成，剩余待处理: " + (pendingPedCreations - 1) + " ===");
            pendingPedCreations--;
            Refresh();

            // 如果还有待处理的行人，延迟触发下一个（避免时序冲突）
            if (pendingPedCreations > 0)
            {
                // Debug.Log("等待0.2秒后触发下一个行人的FSM流程...");
                StartCoroutine(TriggerPedFSMWithDelay());
            }
            else
            {
                Debug.Log("所有行人创建流程完成！");
                pendingPedCreations = 0; // 重置计数器
            }
        }
        // 延迟触发车辆FSM的Coroutine
        private IEnumerator TriggerCarFSMWithDelay()
        {
            yield return new WaitForSeconds(0.2f); // 等待0.2秒让当前FSM完全完成
            // Debug.Log("延迟完成，现在触发下一个车辆FSM");
            TriggerCarFSMWaitForInput();
        }
        private IEnumerator TriggerDeliveryFSMWithDelay()
        {
            yield return new WaitForSeconds(0.2f); // 等待0.2秒让当前FSM完全完成
            // Debug.Log("延迟完成，现在触发下一个车辆FSM");
            TriggerDeliveryFSMWaitForInput();
        }

        // 延迟触发行人FSM的Coroutine
        private IEnumerator TriggerPedFSMWithDelay()
        {
            yield return new WaitForSeconds(0.2f); // 等待0.2秒让当前FSM完全完成
            // Debug.Log("延迟完成，现在触发下一个行人FSM");
            TriggerPedFSMWaitForInput();
        }

        private void TriggerPedFSMWaitForInput()
        {
            // 获取AI_PED对象上的PlayMakerFSM组件
            PlayMakerFSM pedFSM = AI_PED.GetComponent<PlayMakerFSM>();
            if (pedFSM != null)
            {
                // 发送FINISHED事件到FSM，触发Wait for input状态
                pedFSM.SendEvent("FINISHED");
                // Debug.Log("触发AI_PED FSM的FINISHED事件");
            }
            else
            {
                Debug.LogWarning("AI_PED对象上未找到PlayMakerFSM组件");
            }
        }

        // 删除单个车辆（供按钮调用）
        private void DeleteSingleCar()
        {
            if (AI_CAR.transform.childCount > 0)
            {
                GameObject.Destroy(AI_CAR.transform.GetChild(0).gameObject);
                // 由于GameObject.Destroy是异步的，需要延迟更新FSM变量和刷新UI
                Invoke("UpdateCarFSMVariable", 0.1f);
                Invoke("Refresh", 0.1f);
            }
        }
        // 删除单个运输车辆（供按钮调用）
        private void DeleteSingleDelivery()
        {
            if (AI_DELIVERY.transform.childCount > 0)
            {
                GameObject.Destroy(AI_DELIVERY.transform.GetChild(0).gameObject);
                // 由于GameObject.Destroy是异步的，需要延迟更新FSM变量和刷新UI
                Invoke("UpdateDeliveryFSMVariable", 0.1f);
                Invoke("Refresh", 0.1f);
            }
        }

        // 删除单个行人（供按钮调用）
        private void DeleteSinglePed()
        {
            if (AI_PED.transform.childCount > 0)
            {
                GameObject.Destroy(AI_PED.transform.GetChild(0).gameObject);
                // 由于GameObject.Destroy是异步的，需要延迟刷新UI
                Invoke("Refresh", 0.1f);
            }
        }

        // 批量删除多个车辆
        private void DeleteMultipleCars(int deleteCount)
        {
            // 先收集要删除的对象，避免Destroy异步特性导致的问题
            List<GameObject> objectsToDelete = new List<GameObject>();
            int actualDeleteCount = Mathf.Min(deleteCount, AI_CAR.transform.childCount);

            for (int i = 0; i < actualDeleteCount; i++)
            {
                if (AI_CAR.transform.childCount > i)
                {
                    objectsToDelete.Add(AI_CAR.transform.GetChild(i).gameObject);
                }
            }

            // 删除收集的对象
            foreach (GameObject obj in objectsToDelete)
            {
                GameObject.Destroy(obj);
            }

            Debug.Log($"实际删除了 {objectsToDelete.Count} 个车辆");
            // 由于GameObject.Destroy是异步的，需要延迟更新FSM变量和刷新UI
            Invoke("UpdateCarFSMVariable", 0.1f);
            Invoke("Refresh", 0.1f);
        }

        // 批量删除多个运输车辆
        private void DeleteMultipleDeliverys(int deleteCount)
        {
            // 先收集要删除的对象，避免Destroy异步特性导致的问题
            List<GameObject> objectsToDelete = new List<GameObject>();
            int actualDeleteCount = Mathf.Min(deleteCount, AI_DELIVERY.transform.childCount);

            for (int i = 0; i < actualDeleteCount; i++)
            {
                if (AI_DELIVERY.transform.childCount > i)
                {
                    objectsToDelete.Add(AI_DELIVERY.transform.GetChild(i).gameObject);
                }
            }

            // 删除收集的对象
            foreach (GameObject obj in objectsToDelete)
            {
                GameObject.Destroy(obj);
            }

            Debug.Log($"实际删除了 {objectsToDelete.Count} 个车辆");
            // 由于GameObject.Destroy是异步的，需要延迟更新FSM变量和刷新UI
            Invoke("UpdateDeliveryFSMVariable", 0.1f);
            Invoke("Refresh", 0.1f);
        }

        // 更新车辆FSM中的howMuch_i变量
        public void UpdateCarFSMVariable()
        {
            PlayMakerFSM carFSM = AI_CAR.GetComponent<PlayMakerFSM>();
            if (carFSM != null)
            {
                // 设置howMuch_i变量为实际的子对象数量
                carFSM.FsmVariables.GetFsmInt("howMuch_i").Value = AI_CAR.transform.childCount;
                Debug.Log($"更新AI_CAR FSM的howMuch_i变量为: {AI_CAR.transform.childCount}");
            }
            else
            {
                Debug.LogWarning("AI_CAR对象上未找到PlayMakerFSM组件");
            }
        }

        // 更新车辆FSM中的howMuch_i变量
        public void UpdateDeliveryFSMVariable()
        {
            PlayMakerFSM deliveryFSM = AI_DELIVERY.GetComponent<PlayMakerFSM>();
            if (deliveryFSM != null)
            {
                // 设置howMuch_i变量为实际的子对象数量
                deliveryFSM.FsmVariables.GetFsmInt("howMuch_i").Value = AI_DELIVERY.transform.childCount;
                Debug.Log($"更新AI_DELIVERY FSM的howMuch_i变量为: {AI_DELIVERY.transform.childCount}");
            }
            else
            {
                Debug.LogWarning("AI_DELIVERY对象上未找到PlayMakerFSM组件");
            }
        }



        // 批量删除多个行人
        private void DeleteMultiplePeds(int deleteCount)
        {
            // 先收集要删除的对象，避免Destroy异步特性导致的问题
            List<GameObject> objectsToDelete = new List<GameObject>();
            int actualDeleteCount = Mathf.Min(deleteCount, AI_PED.transform.childCount);

            for (int i = 0; i < actualDeleteCount; i++)
            {
                if (AI_PED.transform.childCount > i)
                {
                    objectsToDelete.Add(AI_PED.transform.GetChild(i).gameObject);
                }
            }

            // 删除收集的对象
            foreach (GameObject obj in objectsToDelete)
            {
                GameObject.Destroy(obj);
            }

            Debug.Log($"实际删除了 {objectsToDelete.Count} 个行人");
            // 由于GameObject.Destroy是异步的，需要延迟刷新UI
            Invoke("Refresh", 0.1f);
        }

    }
}