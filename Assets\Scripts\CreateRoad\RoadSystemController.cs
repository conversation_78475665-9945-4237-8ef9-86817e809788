using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

namespace VIC.Road
{
    public class RoadSystemController : MonoBehaviour
    {
        [Header("��Դ")]
        [SerializeField]
        public RoadDataGroup dataGrounp;

        public GameObject IconItemPrefab;
        public Transform ItemIconRoot;
        [Header("����")]
        public Transform GridGroup;
        public Grid[] grids;
        /// <summary>
        /// װ����������
        /// </summary>
        private List<IBuilddingsCommand> commands =new List<IBuilddingsCommand> ();

        [SerializeField]
        private int commandIndex = -1; //---��¼�����±�

        [Header("UI���")]
        public Button UndoButton;
        public Button RedoButton;


        public Button RotateButton;
        public Button RotateConfirmButton;
        public Button CloseButton;
        public Button CompleteButton;

        public RectTransform RotateRoot;//--����UI

        

        public Camera mainCamera;//����Ӱ��
        [SerializeField]
        private Vector3 defaultStartPos;
        private Grid defaultGrid;
        private List<PutRoadInfo> putRoadInfos = new List<PutRoadInfo>();
        private bool isDone =true;//---�жϵ�ǰ�����Ƿ�ɹ�����
        [Header("�����赲���")]
        public GameObject Canvas;
        public EventSystem es;
        

        private void Awake()
        {
            UndoButton.onClick.AddListener(OnClickUnDo);
            mainCamera =Camera.main;
            grids =GridGroup.GetComponentsInChildren<Grid> ();
        }
        private void Start()
        {
            UndoButton.interactable = false;
            CreateRoadIcon(RoadType.Normal);
        }
        public void RemoveRoad(PutRoadInfo putRoadInfo,bool isDestory =false)
        {
            putRoadInfos.Remove(putRoadInfo);
            if(isDestory)
            {
                GameObject.Destroy(putRoadInfo.Road);
                putRoadInfo.Grid.isRoad = false;
                putRoadInfo.Grid = null;
            }
        }
        /// <summary>
        /// �����жϳ�����ť�Ƿ�ɽ���
        /// </summary>
        public void SyncUndoRedoButtons()
        {
            UndoButton.interactable = commands.Count > 0 && commandIndex >= 0 && 
                commandIndex < commands.Count;
        }
        private void OnClickUnDo()
        {
            if(commandIndex>0)
            {
                IBuilddingsCommand command = commands[commandIndex];
                command.Undo();
                commandIndex--;
            }
            SyncUndoRedoButtons();
        }
        public void AddCommand(IBuilddingsCommand newCommand)
        {
            for (int i = commands.Count-1; i >commandIndex; i--)
            {
                commands.RemoveAt(i);
            }
            commands.Add(newCommand);
            newCommand.Execute();
            putRoadInfos.Add(newCommand.putRoadInfo);
            commandIndex++;
            SyncUndoRedoButtons();
        }
        /// <summary>
        /// �������ݵĴ��봴����·��ͼ�겢����������
        /// </summary>
        public void CreateRoadIcon(RoadType roadType)
        {
            for (int i = 0; i < dataGrounp.dataGrounp.Length; i++)
            {
                if(roadType ==dataGrounp.dataGrounp[i].RoadType)
                {
                    for (int j = 0; j < dataGrounp.dataGrounp[i].RoadBaseDatas.Length; j++)
                    {
                        var data = dataGrounp.dataGrounp[i].RoadBaseDatas[j];
                        CreateRoad(data);
                    }
                }
            }
        }
        public void RemoveRoadIcon()
        {
            for (int i = 0; i < ItemIconRoot.childCount; i++)
            {
                Destroy(ItemIconRoot.GetChild(i).gameObject);
            }
        }
        /// <summary>
        /// ����·��ʵ��
        /// </summary>
        /// <param name="data"></param>
        private void CreateRoad(RoadBaseData data)
        {
            var item = GameObject.Instantiate(IconItemPrefab, ItemIconRoot);
            Image image = item.GetComponentInChildren<Image>();
            if(image!=null)
            image.sprite = data.Icon;
            var button = item.GetComponent<Button>();
            button.onClick.AddListener(() =>
            {
                //NewRoadCommand newRoadCommad = new NewRoadCommand();
                //newRoadCommad.controller = this;
                //PutRoadInfo info = new PutRoadInfo()
                //{
                //    //��Ҫ�޸�Grid��Ϣ
                //    Road = null,
                //    Grid = FindEmptyGrid(),
                //    Rotation = Quaternion.identity,
                //    Template = data.Prefab,
                //};
                //newRoadCommad.putRoadInfo = info;
                //AddCommand(newRoadCommad);
                //RotateRoot.gameObject.SetActive(false);
                if(isDone)
                StartCoroutine(BuildRoad(data));
                RotateRoot.gameObject.SetActive(false);
            });
        }
        /// <summary>
        /// Я�̴���·
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        IEnumerator BuildRoad(RoadBaseData data)
        {
            yield return null;
            NewRoadCommand newRoadCommad = new NewRoadCommand();
            newRoadCommad.controller = this;
            PutRoadInfo info = new PutRoadInfo()
            {
                //��Ҫ�޸�Grid��Ϣ
                Road = null,
                Rotation = Quaternion.identity,
                Template = data.Prefab,
            };
            isDone = false;

            GameObject obj = GameObject.Instantiate(info.Template);
            while(info.Grid==null)
            {
                yield return null;
                var screenPos =Input.mousePosition;
                var temp1 = mainCamera.WorldToScreenPoint(obj.transform.position).z;
                screenPos.z = temp1;
                var position = mainCamera.ScreenToWorldPoint(screenPos);

                Ray tempray = mainCamera.ScreenPointToRay(Input.mousePosition);
                RaycastHit temphit;
                
                if (Physics.Raycast(tempray, out temphit, 1000, LayerMask.GetMask("Grid")))
                {
                    position = temphit.transform.position;
                }
                obj.transform.position = position;

                if (Input.GetMouseButtonDown(0)&&!CheckGuiRaycastObjects())
                {
                    Ray ray = mainCamera.ScreenPointToRay(Input.mousePosition);
                    RaycastHit hit;
                    if (Physics.Raycast(ray, out hit, 1000, LayerMask.GetMask("Grid"))&&!hit.transform.GetComponent<Grid>().isRoad)
                    {
                        if(hit.transform.GetComponent<Grid>())
                        info.Grid = hit.transform.GetComponent<Grid>();
                        info.Grid.isRoad = true;
                    }
                }
            }
            GameObject.Destroy(obj);
            isDone = true;
            newRoadCommad.putRoadInfo = info;
            AddCommand(newRoadCommad);
            RotateRoot.gameObject.SetActive(false);
            
        }
        bool CheckGuiRaycastObjects()
        {
            PointerEventData eventData = new PointerEventData(es);
            eventData.pressPosition = Input.mousePosition;
            eventData.position = Input.mousePosition;


            List<RaycastResult> list = new List<RaycastResult>();
            Canvas.GetComponent<GraphicRaycaster>().Raycast(eventData, list);
            return list.Count > 0;
        }
    }
}


