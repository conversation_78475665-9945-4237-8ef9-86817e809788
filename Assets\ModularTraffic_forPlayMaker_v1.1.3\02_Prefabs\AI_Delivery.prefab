%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &108904
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 496118}
  - component: {fileID: 11424952}
  - component: {fileID: 11466640}
  - component: {fileID: 11455448}
  - component: {fileID: 11469472}
  - component: {fileID: 11486228}
  - component: {fileID: 11498232}
  - component: {fileID: 11446606}
  - component: {fileID: 11488786}
  - component: {fileID: 11440224}
  - component: {fileID: 11473998}
  - component: {fileID: 11441826}
  - component: {fileID: 11401864}
  - component: {fileID: 11435798}
  - component: {fileID: 3029714609040087604}
  - component: {fileID: 2781451303950235560}
  m_Layer: 0
  m_Name: AI_Delivery
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &496118
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 108904}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.572, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &11424952
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 108904}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1384774831, guid: de72a6d2da64d114d95e3c5a01cfaec5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fsm:
    dataVersion: 1
    usedInTemplate: {fileID: 0}
    name: SETUP
    startState: Owner
    states:
    - name: Wheel Coll
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 64
        y: 320
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Wheel Mesh
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.FindChild
        - HutongGames.PlayMaker.Actions.FindChild
        - HutongGames.PlayMaker.Actions.FindChild
        - HutongGames.PlayMaker.Actions.FindChild
        - HutongGames.PlayMaker.Actions.Comment
        - HutongGames.PlayMaker.Actions.GetComponent
        - HutongGames.PlayMaker.Actions.GetComponent
        - HutongGames.PlayMaker.Actions.GetComponent
        - HutongGames.PlayMaker.Actions.GetComponent
        customNames:
        - 
        - 
        - 
        - 
        - 
        - 
        - 
        - 
        - 
        actionEnabled: 010101010101010101
        actionIsOpen: 010101010101010101
        actionStartIndex: 000000000300000006000000090000000c0000000d000000100000001300000016000000
        actionHashCodes: f90ba701f90ba701f90ba701f90ba70178c5300104d8140204d8140204d8140204d81402
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: WheelColl_FL_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: WheelColl_FR_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: WheelColl_RL_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: WheelColl_RR_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: CarPrefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: CarPrefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: CarPrefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: CarPrefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: WheelColl_FL_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: WheelColl_FR_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: WheelColl_RL_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: WheelColl_RR_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: WheelColl/FL
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: WheelColl/FR
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: WheelColl/RL
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: WheelColl/RR
        fsmObjectParams:
        - useVariable: 1
          name: WheelCollComp_FL_o
          tooltip: 
          showInInspector: 0
          networkSync: 0
          typeName: UnityEngine.WheelCollider
          value: {fileID: 0}
        - useVariable: 1
          name: WheelCollComp_FR_o
          tooltip: 
          showInInspector: 0
          networkSync: 0
          typeName: UnityEngine.WheelCollider
          value: {fileID: 0}
        - useVariable: 1
          name: WheelCollComp_RL_o
          tooltip: 
          showInInspector: 0
          networkSync: 0
          typeName: UnityEngine.WheelCollider
          value: {fileID: 0}
        - useVariable: 1
          name: WheelCollComp_RR_o
          tooltip: 
          showInInspector: 0
          networkSync: 0
          typeName: UnityEngine.WheelCollider
          value: {fileID: 0}
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 00000000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 14000000120000001300000014000000120000001300000014000000120000001300000014000000120000001300000003000000140000001800000001000000140000001800000001000000140000001800000001000000140000001800000001000000
        paramName:
        - gameObject
        - childName
        - storeResult
        - gameObject
        - childName
        - storeResult
        - gameObject
        - childName
        - storeResult
        - gameObject
        - childName
        - storeResult
        - comment
        - gameObject
        - storeComponent
        - everyFrame
        - gameObject
        - storeComponent
        - everyFrame
        - gameObject
        - storeComponent
        - everyFrame
        - gameObject
        - storeComponent
        - everyFrame
        paramDataPos: 00000000000000000000000001000000010000000100000002000000020000000200000003000000030000000300000000000000040000000000000000000000050000000100000001000000060000000200000002000000070000000300000003000000
        paramByteDataSize: 00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001000000000000000000000001000000000000000000000001000000000000000000000001000000
    - name: Wheel Mesh
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 64
        y: 368
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Lights
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.FindChild
        - HutongGames.PlayMaker.Actions.FindChild
        - HutongGames.PlayMaker.Actions.FindChild
        - HutongGames.PlayMaker.Actions.FindChild
        - HutongGames.PlayMaker.Actions.Comment
        - HutongGames.PlayMaker.Actions.FindChild
        - HutongGames.PlayMaker.Actions.FindChild
        - HutongGames.PlayMaker.Actions.FindChild
        - HutongGames.PlayMaker.Actions.FindChild
        customNames:
        - 
        - 
        - 
        - 
        - 
        - 
        - 
        - 
        - 
        actionEnabled: 010101010101010101
        actionIsOpen: 010101010101010101
        actionStartIndex: 000000000300000006000000090000000c0000000d000000100000001300000016000000
        actionHashCodes: f90ba701f90ba701f90ba701f90ba70178c53001f90ba701f90ba701f90ba701f90ba701
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: WheelMeshEmpty_FL_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: WheelMeshEmpty_FR_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: WheelMeshEmpty_RL_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: WheelMeshEmpty_RR_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: WheelMesh_FL_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: WheelMesh_FR_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: WheelMesh_RL_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: WheelMesh_RR_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: CarPrefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: CarPrefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: CarPrefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: CarPrefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: WheelMeshEmpty_FL_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: WheelMeshEmpty_FR_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: WheelMeshEmpty_RL_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: WheelMeshEmpty_RR_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: WheelMesh/FL
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: WheelMesh/FR
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: WheelMesh/RL
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: WheelMesh/RR
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: CarWheel
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: CarWheel
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: CarWheel
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: CarWheel
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 14000000120000001300000014000000120000001300000014000000120000001300000014000000120000001300000003000000140000001200000013000000140000001200000013000000140000001200000013000000140000001200000013000000
        paramName:
        - gameObject
        - childName
        - storeResult
        - gameObject
        - childName
        - storeResult
        - gameObject
        - childName
        - storeResult
        - gameObject
        - childName
        - storeResult
        - comment
        - gameObject
        - childName
        - storeResult
        - gameObject
        - childName
        - storeResult
        - gameObject
        - childName
        - storeResult
        - gameObject
        - childName
        - storeResult
        paramDataPos: 00000000000000000000000001000000010000000100000002000000020000000200000003000000030000000300000000000000040000000400000004000000050000000500000005000000060000000600000006000000070000000700000007000000
        paramByteDataSize: 00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
    - name: Car setup
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 64
        y: 608
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: LookAt
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetComponent
        - HutongGames.PlayMaker.Actions.GetFsmVector3
        - HutongGames.PlayMaker.Actions.SetProperty
        customNames:
        - 
        - 
        - 
        actionEnabled: 010101
        actionIsOpen: 010101
        actionStartIndex: 000000000300000008000000
        actionHashCodes: 04d81402bf9f37025ac85204
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: CarPrefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: CarPrefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams:
        - TargetObject:
            useVariable: 1
            name: CarRigdb_o
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: UnityEngine.Rigidbody
            value: {fileID: 0}
          TargetTypeName: UnityEngine.Rigidbody
          PropertyName: centerOfMass
          BoolParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          FloatParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          IntParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          GameObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
          StringParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 
          Vector2Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0}
          Vector3Parameter:
            useVariable: 1
            name: CarRigdbCentrMass_v
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0}
          RectParamater:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value:
              serializedVersion: 2
              x: 0
              y: 0
              width: 0
              height: 0
          QuaternionParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0, w: 0}
          ObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: UnityEngine.Vector3
            value: {fileID: 0}
          MaterialParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          TextureParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          ColorParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {r: 0, g: 0, b: 0, a: 1}
          EnumParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            enumName: 
            intValue: 0
          ArrayParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            type: -1
            objectTypeName: 
            floatValues: []
            intValues: 
            boolValues: 
            stringValues: []
            vector4Values: []
            objectReferences: []
          setProperty: 1
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: CentrMass_v
        fsmObjectParams:
        - useVariable: 1
          name: CarRigdb_o
          tooltip: 
          showInInspector: 0
          networkSync: 0
          typeName: UnityEngine.Rigidbody
          value: {fileID: 0}
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 0000000000000000000000000001436172526967646243656e74724d6173735f760000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1400000018000000010000001400000012000000120000001c000000010000002400000001000000
        paramName:
        - gameObject
        - storeComponent
        - everyFrame
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        - targetProperty
        - everyFrame
        paramDataPos: 00000000000000000000000001000000000000000100000001000000210000000000000022000000
        paramByteDataSize: 00000000000000000100000000000000000000000000000020000000010000000000000001000000
    - name: -DONE-
      description: 
      colorIndex: 4
      position:
        serializedVersion: 2
        x: 64
        y: 752
        width: 100
        height: 16
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions: []
      actionData:
        actionNames: []
        customNames: []
        actionEnabled: 
        actionIsOpen: 
        actionStartIndex: 
        actionHashCodes: 
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 
        paramName: []
        paramDataPos: 
        paramByteDataSize: 
    - name: Owner
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 592
        y: 112
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: 05 Cargo2
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetOwner
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: 5c004505
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Owner_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 13000000
        paramName:
        - storeGameObject
        paramDataPos: 00000000
        paramByteDataSize: 00000000
    - name: LookAt
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 64
        y: 656
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: -DONE-
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.FindChild
        - HutongGames.PlayMaker.Actions.FindChild
        - HutongGames.PlayMaker.Actions.FindChild
        customNames:
        - 
        - 
        - 
        actionEnabled: 010101
        actionIsOpen: 010101
        actionStartIndex: 000000000300000006000000
        actionHashCodes: f90ba701f90ba701f90ba701
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: LookAt_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: Ray1_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: Ray2_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: CarPrefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: LookAt_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: LookAt_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: LookAt
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Ray1
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Ray2
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 140000001200000013000000140000001200000013000000140000001200000013000000
        paramName:
        - gameObject
        - childName
        - storeResult
        - gameObject
        - childName
        - storeResult
        - gameObject
        - childName
        - storeResult
        paramDataPos: 000000000000000000000000010000000100000001000000020000000200000002000000
        paramByteDataSize: 000000000000000000000000000000000000000000000000000000000000000000000000
    - name: Lights
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 64
        y: 480
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Car setup
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.FindChild
        - HutongGames.PlayMaker.Actions.FindChild
        - HutongGames.PlayMaker.Actions.FindChild
        customNames:
        - 
        - 
        - 
        actionEnabled: 010101
        actionIsOpen: 010101
        actionStartIndex: 000000000300000006000000
        actionHashCodes: f90ba701f90ba701f90ba701
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: LightsLEFT_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: LightsRIGHT_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: LightsBrakes_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: CarPrefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: CarPrefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: CarPrefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Lights/Turn_Left
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Lights/Turn_Right
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Lights/Brakes
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 140000001200000013000000140000001200000013000000140000001200000013000000
        paramName:
        - gameObject
        - childName
        - storeResult
        - gameObject
        - childName
        - storeResult
        - gameObject
        - childName
        - storeResult
        paramDataPos: 000000000000000000000000010000000100000001000000020000000200000002000000
        paramByteDataSize: 000000000000000000000000000000000000000000000000000000000000000000000000
    - name: 05 Cargo2
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 357.84375
        y: 273.7422
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Wheel Coll
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.CreateObject
        - HutongGames.PlayMaker.Actions.SetParent
        - HutongGames.PlayMaker.Actions.NextFrameEvent
        customNames:
        - 
        - 
        - 
        actionEnabled: 010101
        actionIsOpen: 010101
        actionStartIndex: 00000000060000000b000000
        actionHashCodes: 60532301a0dd830388611604
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 175044, guid: 5c3c8047a072d469eb9def57b2698d99, type: 3}
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: Owner_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: CarPrefab_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: Owner_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: CarPrefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 000000000000000000000000010000000000000000000000000101000000000046494e4953484544
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1300000013000000130000001c0000001c00000013000000140000001300000011000000110000001100000017000000
        paramName:
        - gameObject
        - parent
        - spawnPoint
        - position
        - rotation
        - storeObject
        - gameObject
        - parent
        - worldPositionStays
        - resetLocalPosition
        - resetLocalRotation
        - sendEvent
        paramDataPos: 000000000100000002000000000000000d0000000300000000000000040000001a0000001c0000001e00000020000000
        paramByteDataSize: 0000000000000000000000000d0000000d00000000000000000000000000000002000000020000000200000008000000
    events:
    - name: FINISHED
      isSystemEvent: 1
      isGlobal: 0
    - name: Nr/L/001
      isSystemEvent: 0
      isGlobal: 0
    - name: Nr/L/002
      isSystemEvent: 0
      isGlobal: 0
    - name: Nr/L/003
      isSystemEvent: 0
      isGlobal: 0
    - name: Nr/L/004
      isSystemEvent: 0
      isGlobal: 0
    - name: Nr/L/005
      isSystemEvent: 0
      isGlobal: 0
    - name: Nr/L/006
      isSystemEvent: 0
      isGlobal: 0
    - name: Nr/L/007
      isSystemEvent: 0
      isGlobal: 0
    - name: Nr/L/008
      isSystemEvent: 0
      isGlobal: 0
    globalTransitions: []
    variables:
      floatVariables: []
      intVariables: []
      boolVariables: []
      stringVariables: []
      vector2Variables: []
      vector3Variables:
      - useVariable: 1
        name: CarRigdbCentrMass_v
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {x: 0, y: 0, z: 0}
      colorVariables: []
      rectVariables: []
      quaternionVariables: []
      gameObjectVariables:
      - useVariable: 1
        name: CarPrefab_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: LightsBrakes_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: LightsLEFT_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: LightsRIGHT_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: LookAt_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: Owner_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: Ray1_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: Ray2_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: WheelColl_FL_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: WheelColl_FR_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: WheelColl_RL_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: WheelColl_RR_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: WheelMeshEmpty_FL_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: WheelMeshEmpty_FR_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: WheelMeshEmpty_RL_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: WheelMeshEmpty_RR_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: WheelMesh_FL_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: WheelMesh_FR_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: WheelMesh_RL_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: WheelMesh_RR_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      objectVariables:
      - useVariable: 1
        name: CarRigdb_o
        tooltip: 
        showInInspector: 0
        networkSync: 0
        typeName: UnityEngine.Rigidbody
        value: {fileID: 0}
      - useVariable: 1
        name: WheelCollComp_FL_o
        tooltip: 
        showInInspector: 0
        networkSync: 0
        typeName: UnityEngine.WheelCollider
        value: {fileID: 0}
      - useVariable: 1
        name: WheelCollComp_FR_o
        tooltip: 
        showInInspector: 0
        networkSync: 0
        typeName: UnityEngine.WheelCollider
        value: {fileID: 0}
      - useVariable: 1
        name: WheelCollComp_RL_o
        tooltip: 
        showInInspector: 0
        networkSync: 0
        typeName: UnityEngine.WheelCollider
        value: {fileID: 0}
      - useVariable: 1
        name: WheelCollComp_RR_o
        tooltip: 
        showInInspector: 0
        networkSync: 0
        typeName: UnityEngine.WheelCollider
        value: {fileID: 0}
      materialVariables: []
      textureVariables: []
      arrayVariables: []
      enumVariables: []
      categories:
      - 
      variableCategoryIDs: 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
    description: '




      This FSM gets all parts needed for other Fsm''s, others
      wait until this Fsm is at -DONE- state and takes values to function.


      ================='
    docUrl: 
    showStateLabel: 0
    maxLoopCount: 0
    watermark: 
    password: 
    locked: 0
    manualUpdate: 0
    outVariableIndices: 
    keepDelayedEventsOnStateExit: 0
    preprocessed: 0
    ExposedEvents: []
    OutputEvents: []
    RestartOnEnable: 1
    ResetVariablesOnEnable: 0
    EnableDebugFlow: 0
    EnableBreakpoints: 1
    editorFlags: 2
    activeStateName: 
    mouseEvents: 0
    handleLevelLoaded: 0
    handleTriggerEnter2D: 0
    handleTriggerExit2D: 0
    handleTriggerStay2D: 0
    handleCollisionEnter2D: 0
    handleCollisionExit2D: 0
    handleCollisionStay2D: 0
    handleTriggerEnter: 0
    handleTriggerExit: 0
    handleTriggerStay: 0
    handleCollisionEnter: 0
    handleCollisionExit: 0
    handleCollisionStay: 0
    handleParticleCollision: 0
    handleControllerColliderHit: 0
    handleJointBreak: 0
    handleJointBreak2D: 0
    handleOnGUI: 0
    handleFixedUpdate: 0
    handleLateUpdate: 0
    handleApplicationEvents: 0
    handleUiEvents: 0
    handleLegacyNetworking: 0
    handleAnimatorMove: 0
    handleAnimatorIK: 0
  fsmTemplate: {fileID: 0}
  eventHandlerComponentsAdded: 1
--- !u!114 &11466640
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 108904}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1384774831, guid: de72a6d2da64d114d95e3c5a01cfaec5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fsm:
    dataVersion: 1
    usedInTemplate: {fileID: 0}
    name: Movement/Drive
    startState: owner
    states:
    - name: GET
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 64
        y: 240
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: SET
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetFsmGameObject
        - HutongGames.PlayMaker.Actions.GetFsmObject
        - HutongGames.PlayMaker.Actions.GetFsmObject
        - HutongGames.PlayMaker.Actions.GetFsmObject
        - HutongGames.PlayMaker.Actions.GetFsmObject
        customNames:
        - 
        - 
        - 
        - 
        - 
        actionEnabled: 0101010101
        actionIsOpen: 0101010101
        actionStartIndex: 00000000050000000a0000000f00000014000000
        actionHashCodes: 5b66cf0074cb920274cb920274cb920274cb9202
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Prefab_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams:
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: CarPrefab_g
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: WheelCollComp_FL_o
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: WheelCollComp_FR_o
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: WheelCollComp_RL_o
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: WheelCollComp_RR_o
        fsmObjectParams:
        - useVariable: 1
          name: FL_o
          tooltip: 
          showInInspector: 0
          networkSync: 0
          typeName: UnityEngine.WheelCollider
          value: {fileID: 0}
        - useVariable: 1
          name: FR_o
          tooltip: 
          showInInspector: 0
          networkSync: 0
          typeName: UnityEngine.WheelCollider
          value: {fileID: 0}
        - useVariable: 1
          name: RL_o
          tooltip: 
          showInInspector: 0
          networkSync: 0
          typeName: UnityEngine.WheelCollider
          value: {fileID: 0}
        - useVariable: 1
          name: RR_o
          tooltip: 
          showInInspector: 0
          networkSync: 0
          typeName: UnityEngine.WheelCollider
          value: {fileID: 0}
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 0000000000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 14000000120000001200000013000000010000001400000012000000120000001800000001000000140000001200000012000000180000000100000014000000120000001200000018000000010000001400000012000000120000001800000001000000
        paramName:
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        paramDataPos: 00000000000000000100000000000000000000000100000002000000030000000000000001000000020000000400000005000000010000000200000003000000060000000700000002000000030000000400000008000000090000000300000004000000
        paramByteDataSize: 00000000000000000000000000000000010000000000000000000000000000000000000001000000000000000000000000000000000000000100000000000000000000000000000000000000010000000000000000000000000000000000000001000000
    - name: done?
      description: 
      colorIndex: 5
      position:
        serializedVersion: 2
        x: 64
        y: 160
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: GET
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.FsmStateTest
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: 05531600
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Owner_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: -DONE-
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 46494e4953484544000101
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 13000000120000001200000017000000170000001100000001000000
        paramName:
        - gameObject
        - fsmName
        - stateName
        - trueEvent
        - falseEvent
        - storeResult
        - everyFrame
        paramDataPos: 0000000000000000010000000000000008000000080000000a000000
        paramByteDataSize: 00000000000000000000000008000000000000000200000001000000
    - name: owner
      description: 
      colorIndex: 5
      position:
        serializedVersion: 2
        x: 64
        y: 112
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: done?
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetOwner
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: 5c004505
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Owner_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 13000000
        paramName:
        - storeGameObject
        paramDataPos: 00000000
        paramByteDataSize: 00000000
    - name: SET
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 64
        y: 288
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: -Gas-
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.SetFloatValue
        - HutongGames.PlayMaker.Actions.SetFloatValue
        - HutongGames.PlayMaker.Actions.SetFloatValue
        - HutongGames.PlayMaker.Actions.SetFloatValue
        - HutongGames.PlayMaker.Actions.RandomFloat
        customNames:
        - 
        - 
        - 
        - 
        - 
        actionEnabled: 0001010001
        actionIsOpen: 0101010101
        actionStartIndex: 000000000300000006000000090000000c000000
        actionHashCodes: 9845b3059845b3059845b3059845b3052ee01704
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 0000000001416363656c65726174696f6e5f660060864500000000000001416363656c65726174696f6e5f6600401c45000000000000014272616b65735f6600d8d647000000000000016d617853706565645f660000204100000000104100000020410000000000016d617853706565645f66
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 0f0000000f000000010000000f0000000f000000010000000f0000000f000000010000000f0000000f000000010000000f0000000f0000000f000000
        paramName:
        - floatVariable
        - floatValue
        - everyFrame
        - floatVariable
        - floatValue
        - everyFrame
        - floatVariable
        - floatValue
        - everyFrame
        - floatVariable
        - floatValue
        - everyFrame
        - min
        - max
        - storeResult
        paramDataPos: 000000001300000018000000190000002c00000031000000320000003f000000440000004500000054000000590000005a0000005f00000064000000
        paramByteDataSize: 1300000005000000010000001300000005000000010000000d00000005000000010000000f000000050000000100000005000000050000000f000000
    - name: -Gas-
      description: "Movement/Distanced Fsm is looking for \n\"-Gas-\" and \"-Brake-\"
        state names at the start.\n"
      colorIndex: 4
      position:
        serializedVersion: 2
        x: 288
        y: 304
        width: 197
        height: 48
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: MAX
          isSystemEvent: 0
          isGlobal: 0
        toState: MAX
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Lights ON
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetFsmBool
        - HutongGames.PlayMaker.Actions.GetFsmBool
        - HutongGames.PlayMaker.Actions.BoolAnyTrue
        - HutongGames.PlayMaker.Actions.Comment
        - HutongGames.PlayMaker.Actions.SetProperty
        - HutongGames.PlayMaker.Actions.SetProperty
        - HutongGames.PlayMaker.Actions.Comment
        - HutongGames.PlayMaker.Actions.GetSpeed
        - HutongGames.PlayMaker.Actions.FloatCompare
        customNames:
        - 
        - 
        - 
        - 
        - 
        - 
        - 
        - 
        - 
        actionEnabled: 010101010101010101
        actionIsOpen: 010101010101010101
        actionStartIndex: 00000000050000000a000000100000001100000013000000150000001600000019000000
        actionHashCodes: 68fece0568fece058076870578c530015ac852045ac8520478c5300132d594021bcdca04
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams:
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Prefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams:
        - TargetObject:
            useVariable: 1
            name: RL_o
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: UnityEngine.WheelCollider
            value: {fileID: 0}
          TargetTypeName: UnityEngine.WheelCollider
          PropertyName: motorTorque
          BoolParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          FloatParameter:
            useVariable: 1
            name: Acceleration_f
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          IntParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          GameObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
          StringParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 
          Vector2Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0}
          Vector3Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0}
          RectParamater:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value:
              serializedVersion: 2
              x: 0
              y: 0
              width: 0
              height: 0
          QuaternionParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0, w: 0}
          ObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: System.Single
            value: {fileID: 0}
          MaterialParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          TextureParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          ColorParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {r: 0, g: 0, b: 0, a: 1}
          EnumParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            enumName: 
            intValue: 0
          ArrayParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            type: -1
            objectTypeName: 
            floatValues: []
            intValues: 
            boolValues: 
            stringValues: []
            vector4Values: []
            objectReferences: []
          setProperty: 1
        - TargetObject:
            useVariable: 1
            name: RR_o
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: UnityEngine.WheelCollider
            value: {fileID: 0}
          TargetTypeName: UnityEngine.WheelCollider
          PropertyName: motorTorque
          BoolParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          FloatParameter:
            useVariable: 1
            name: Acceleration_f
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          IntParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          GameObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
          StringParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 
          Vector2Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0}
          Vector3Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0}
          RectParamater:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value:
              serializedVersion: 2
              x: 0
              y: 0
              width: 0
              height: 0
          QuaternionParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0, w: 0}
          ObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: System.Single
            value: {fileID: 0}
          MaterialParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          TextureParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          ColorParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {r: 0, g: 0, b: 0, a: 1}
          EnumParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            enumName: 
            intValue: 0
          ArrayParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            type: -1
            objectTypeName: 
            floatValues: []
            intValues: 
            boolValues: 
            stringValues: []
            vector4Values: []
            objectReferences: []
          setProperty: 1
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Movement/Ray
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: DidHit_b
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Movement/Ray
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: DidHit2_b
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 00014469644869745f62010001446964486974325f620100014469644869745f620001446964486974325f6246494e4953484544000101312e0a2d2d2d2d2d2d0a322e0000322e0a2d2d2d2d2d2d2d0a332e000000000143617253706565645f6601000000000143617253706565645f6600000000016d617853706565645f6600000000004d415801
        arrayParamSizes: 02000000
        arrayParamTypes:
        - HutongGames.PlayMaker.FsmBool
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 140000001200000012000000110000000100000014000000120000001200000011000000010000000c0000001100000011000000170000001100000001000000030000002400000001000000240000000100000003000000140000000f000000010000000f0000000f0000000f00000017000000170000001700000001000000
        paramName:
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        - boolVariables
        - 
        - 
        - sendEvent
        - storeResult
        - everyFrame
        - comment
        - targetProperty
        - everyFrame
        - targetProperty
        - everyFrame
        - comment
        - gameObject
        - storeResult
        - everyFrame
        - float1
        - float2
        - tolerance
        - equal
        - lessThan
        - greaterThan
        - everyFrame
        paramDataPos: 000000000000000001000000000000000a0000000100000002000000030000000b000000160000000000000017000000210000002c000000340000003600000037000000000000004300000001000000440000004500000002000000520000006100000062000000710000008000000085000000850000008500000088000000
        paramByteDataSize: 0000000000000000000000000a000000010000000000000000000000000000000b00000001000000000000000a0000000b0000000800000002000000010000000c000000000000000100000000000000010000000d000000000000000f000000010000000f0000000f0000000500000000000000000000000300000001000000
    - name: Next Frame
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 560
        y: 304
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: -Gas-
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.NextFrameEvent
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: 88611604
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 46494e4953484544
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 17000000
        paramName:
        - sendEvent
        paramDataPos: 00000000
        paramByteDataSize: 08000000
    - name: MAX
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 560
        y: 352
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: BACK
          isSystemEvent: 0
          isGlobal: 0
        toState: Next Frame
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.SetProperty
        - HutongGames.PlayMaker.Actions.SetProperty
        - HutongGames.PlayMaker.Actions.Comment
        - HutongGames.PlayMaker.Actions.GetSpeed
        - HutongGames.PlayMaker.Actions.FloatCompare
        customNames:
        - 
        - 
        - 
        - 
        - 
        actionEnabled: 0101010101
        actionIsOpen: 0101010101
        actionStartIndex: 0000000002000000040000000500000008000000
        actionHashCodes: 5ac852045ac8520478c5300132d594021bcdca04
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Prefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams:
        - TargetObject:
            useVariable: 1
            name: RL_o
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: UnityEngine.WheelCollider
            value: {fileID: 0}
          TargetTypeName: UnityEngine.WheelCollider
          PropertyName: motorTorque
          BoolParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          FloatParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          IntParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          GameObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
          StringParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 
          Vector2Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0}
          Vector3Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0}
          RectParamater:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value:
              serializedVersion: 2
              x: 0
              y: 0
              width: 0
              height: 0
          QuaternionParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0, w: 0}
          ObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: System.Single
            value: {fileID: 0}
          MaterialParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          TextureParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          ColorParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {r: 0, g: 0, b: 0, a: 1}
          EnumParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            enumName: 
            intValue: 0
          ArrayParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            type: -1
            objectTypeName: 
            floatValues: []
            intValues: 
            boolValues: 
            stringValues: []
            vector4Values: []
            objectReferences: []
          setProperty: 1
        - TargetObject:
            useVariable: 1
            name: RR_o
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: UnityEngine.WheelCollider
            value: {fileID: 0}
          TargetTypeName: UnityEngine.WheelCollider
          PropertyName: motorTorque
          BoolParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          FloatParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          IntParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          GameObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
          StringParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 
          Vector2Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0}
          Vector3Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0}
          RectParamater:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value:
              serializedVersion: 2
              x: 0
              y: 0
              width: 0
              height: 0
          QuaternionParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0, w: 0}
          ObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: System.Single
            value: {fileID: 0}
          MaterialParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          TextureParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          ColorParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {r: 0, g: 0, b: 0, a: 1}
          EnumParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            enumName: 
            intValue: 0
          ArrayParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            type: -1
            objectTypeName: 
            floatValues: []
            intValues: 
            boolValues: 
            stringValues: []
            vector4Values: []
            objectReferences: []
          setProperty: 1
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 0000312e0a2d2d2d2d2d2d2d0a322e000000000143617253706565645f6601000000000143617253706565645f6600000000016d617853706565645f6600000000004241434b01
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 2400000001000000240000000100000003000000140000000f000000010000000f0000000f0000000f00000017000000170000001700000001000000
        paramName:
        - targetProperty
        - everyFrame
        - targetProperty
        - everyFrame
        - comment
        - gameObject
        - storeResult
        - everyFrame
        - float1
        - float2
        - tolerance
        - equal
        - lessThan
        - greaterThan
        - everyFrame
        paramDataPos: 0000000000000000010000000100000002000000000000000f0000001e0000001f0000002e0000003d00000042000000420000004600000046000000
        paramByteDataSize: 000000000100000000000000010000000d000000000000000f000000010000000f0000000f0000000500000000000000040000000000000001000000
    - name: -Brake-
      description: 
      colorIndex: 4
      position:
        serializedVersion: 2
        x: 560
        y: 448
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Lights OFF
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.SetProperty
        - HutongGames.PlayMaker.Actions.SetProperty
        - HutongGames.PlayMaker.Actions.Comment
        - HutongGames.PlayMaker.Actions.GetFsmBool
        - HutongGames.PlayMaker.Actions.GetFsmBool
        - HutongGames.PlayMaker.Actions.BoolNoneTrue
        customNames:
        - 
        - 
        - 
        - 
        - 
        - 
        actionEnabled: 010101010101
        actionIsOpen: 010101010101
        actionStartIndex: 000000000200000004000000050000000a0000000f000000
        actionHashCodes: 5ac852045ac8520478c5300168fece0568fece0580768705
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams:
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams:
        - TargetObject:
            useVariable: 1
            name: FL_o
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: UnityEngine.WheelCollider
            value: {fileID: 0}
          TargetTypeName: UnityEngine.WheelCollider
          PropertyName: brakeTorque
          BoolParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          FloatParameter:
            useVariable: 1
            name: Brakes_f
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          IntParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          GameObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
          StringParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 
          Vector2Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0}
          Vector3Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0}
          RectParamater:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value:
              serializedVersion: 2
              x: 0
              y: 0
              width: 0
              height: 0
          QuaternionParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0, w: 0}
          ObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: System.Single
            value: {fileID: 0}
          MaterialParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          TextureParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          ColorParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {r: 0, g: 0, b: 0, a: 1}
          EnumParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            enumName: 
            intValue: 0
          ArrayParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            type: -1
            objectTypeName: 
            floatValues: []
            intValues: 
            boolValues: 
            stringValues: []
            vector4Values: []
            objectReferences: []
          setProperty: 1
        - TargetObject:
            useVariable: 1
            name: FR_o
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: UnityEngine.WheelCollider
            value: {fileID: 0}
          TargetTypeName: UnityEngine.WheelCollider
          PropertyName: brakeTorque
          BoolParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          FloatParameter:
            useVariable: 1
            name: Brakes_f
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          IntParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          GameObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
          StringParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 
          Vector2Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0}
          Vector3Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0}
          RectParamater:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value:
              serializedVersion: 2
              x: 0
              y: 0
              width: 0
              height: 0
          QuaternionParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0, w: 0}
          ObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: System.Single
            value: {fileID: 0}
          MaterialParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          TextureParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          ColorParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {r: 0, g: 0, b: 0, a: 1}
          EnumParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            enumName: 
            intValue: 0
          ArrayParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            type: -1
            objectTypeName: 
            floatValues: []
            intValues: 
            boolValues: 
            stringValues: []
            vector4Values: []
            objectReferences: []
          setProperty: 1
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Movement/Ray
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: DidHit_b
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Movement/Ray
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: DidHit2_b
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 0000312e0a2d2d2d2d2d2d0a322e00014469644869745f62010001446964486974325f620100014469644869745f620001446964486974325f6246494e4953484544000101
        arrayParamSizes: 02000000
        arrayParamTypes:
        - HutongGames.PlayMaker.FsmBool
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 2400000001000000240000000100000003000000140000001200000012000000110000000100000014000000120000001200000011000000010000000c0000001100000011000000170000001100000001000000
        paramName:
        - targetProperty
        - everyFrame
        - targetProperty
        - everyFrame
        - comment
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        - boolVariables
        - 
        - 
        - sendEvent
        - storeResult
        - everyFrame
        paramDataPos: 00000000000000000100000001000000020000000000000000000000010000000e00000018000000010000000200000003000000190000002400000000000000250000002f0000003a0000004200000044000000
        paramByteDataSize: 000000000100000000000000010000000c0000000000000000000000000000000a000000010000000000000000000000000000000b00000001000000000000000a0000000b000000080000000200000001000000
    - name: Lights ON
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 352
        y: 448
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: -Brake-
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.SendEvent
        - HutongGames.PlayMaker.Actions.Comment
        - HutongGames.PlayMaker.Actions.SetProperty
        - HutongGames.PlayMaker.Actions.SetProperty
        customNames:
        - 
        - 
        - 
        - 
        actionEnabled: 01010101
        actionIsOpen: 01010101
        actionStartIndex: 00000000040000000500000007000000
        actionHashCodes: 5e69bb0478c530015ac852045ac85204
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams:
        - target: 2
          excludeSelf:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          gameObject:
            ownerOption: 0
            gameObject:
              useVariable: 0
              name: 
              tooltip: 
              showInInspector: 0
              networkSync: 0
              value: {fileID: 0}
          fsmName:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: Lights/Brakes
          sendToChildren:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          fsmComponent: {fileID: 0}
        fsmPropertyParams:
        - TargetObject:
            useVariable: 1
            name: RL_o
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: UnityEngine.WheelCollider
            value: {fileID: 0}
          TargetTypeName: UnityEngine.WheelCollider
          PropertyName: motorTorque
          BoolParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          FloatParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          IntParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          GameObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
          StringParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 
          Vector2Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0}
          Vector3Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0}
          RectParamater:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value:
              serializedVersion: 2
              x: 0
              y: 0
              width: 0
              height: 0
          QuaternionParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0, w: 0}
          ObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: System.Single
            value: {fileID: 0}
          MaterialParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          TextureParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          ColorParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {r: 0, g: 0, b: 0, a: 1}
          EnumParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            enumName: 
            intValue: 0
          ArrayParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            type: -1
            objectTypeName: 
            floatValues: []
            intValues: 
            boolValues: 
            stringValues: []
            vector4Values: []
            objectReferences: []
          setProperty: 1
        - TargetObject:
            useVariable: 1
            name: RR_o
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: UnityEngine.WheelCollider
            value: {fileID: 0}
          TargetTypeName: UnityEngine.WheelCollider
          PropertyName: motorTorque
          BoolParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          FloatParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          IntParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          GameObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
          StringParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 
          Vector2Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0}
          Vector3Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0}
          RectParamater:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value:
              serializedVersion: 2
              x: 0
              y: 0
              width: 0
              height: 0
          QuaternionParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0, w: 0}
          ObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: System.Single
            value: {fileID: 0}
          MaterialParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          TextureParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          ColorParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {r: 0, g: 0, b: 0, a: 1}
          EnumParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            enumName: 
            intValue: 0
          ArrayParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            type: -1
            objectTypeName: 
            floatValues: []
            intValues: 
            boolValues: 
            stringValues: []
            vector4Values: []
            objectReferences: []
          setProperty: 1
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 472f4f4e000000000000312e0a2d2d2d2d2d2d0a322e0000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1f000000170000000f000000010000000300000024000000010000002400000001000000
        paramName:
        - eventTarget
        - sendEvent
        - delay
        - everyFrame
        - comment
        - targetProperty
        - everyFrame
        - targetProperty
        - everyFrame
        paramDataPos: 000000000000000004000000090000000a00000000000000160000000100000017000000
        paramByteDataSize: 000000000400000005000000010000000c00000000000000010000000000000001000000
    - name: Lights OFF
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 736
        y: 448
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Next Frame
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.SendEvent
        - HutongGames.PlayMaker.Actions.Comment
        - HutongGames.PlayMaker.Actions.SetProperty
        - HutongGames.PlayMaker.Actions.SetProperty
        customNames:
        - 
        - 
        - 
        - 
        actionEnabled: 01010101
        actionIsOpen: 01010101
        actionStartIndex: 00000000040000000500000007000000
        actionHashCodes: 5e69bb0478c530015ac852045ac85204
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams:
        - target: 2
          excludeSelf:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          gameObject:
            ownerOption: 0
            gameObject:
              useVariable: 0
              name: 
              tooltip: 
              showInInspector: 0
              networkSync: 0
              value: {fileID: 0}
          fsmName:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: Lights/Brakes
          sendToChildren:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          fsmComponent: {fileID: 0}
        fsmPropertyParams:
        - TargetObject:
            useVariable: 1
            name: FL_o
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: UnityEngine.WheelCollider
            value: {fileID: 0}
          TargetTypeName: UnityEngine.WheelCollider
          PropertyName: brakeTorque
          BoolParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          FloatParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          IntParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          GameObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
          StringParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 
          Vector2Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0}
          Vector3Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0}
          RectParamater:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value:
              serializedVersion: 2
              x: 0
              y: 0
              width: 0
              height: 0
          QuaternionParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0, w: 0}
          ObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: System.Single
            value: {fileID: 0}
          MaterialParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          TextureParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          ColorParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {r: 0, g: 0, b: 0, a: 1}
          EnumParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            enumName: 
            intValue: 0
          ArrayParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            type: -1
            objectTypeName: 
            floatValues: []
            intValues: 
            boolValues: 
            stringValues: []
            vector4Values: []
            objectReferences: []
          setProperty: 1
        - TargetObject:
            useVariable: 1
            name: FR_o
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: UnityEngine.WheelCollider
            value: {fileID: 0}
          TargetTypeName: UnityEngine.WheelCollider
          PropertyName: brakeTorque
          BoolParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          FloatParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          IntParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          GameObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
          StringParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 
          Vector2Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0}
          Vector3Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0}
          RectParamater:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value:
              serializedVersion: 2
              x: 0
              y: 0
              width: 0
              height: 0
          QuaternionParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0, w: 0}
          ObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: System.Single
            value: {fileID: 0}
          MaterialParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          TextureParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          ColorParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {r: 0, g: 0, b: 0, a: 1}
          EnumParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            enumName: 
            intValue: 0
          ArrayParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            type: -1
            objectTypeName: 
            floatValues: []
            intValues: 
            boolValues: 
            stringValues: []
            vector4Values: []
            objectReferences: []
          setProperty: 1
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 472f4f4646000000000000312e0a2d2d2d2d2d2d0a322e0000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1f000000170000000f000000010000000300000024000000010000002400000001000000
        paramName:
        - eventTarget
        - sendEvent
        - delay
        - everyFrame
        - comment
        - targetProperty
        - everyFrame
        - targetProperty
        - everyFrame
        paramDataPos: 0000000000000000050000000a0000000b00000000000000170000000100000018000000
        paramByteDataSize: 000000000500000005000000010000000c00000000000000010000000000000001000000
    - name: Move
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 336
        y: 624
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: 'Lights ON '
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.FloatInterpolate
        - HutongGames.PlayMaker.Actions.MoveTowards
        - HutongGames.PlayMaker.Actions.GetFsmBool
        - HutongGames.PlayMaker.Actions.BoolTest
        customNames:
        - 
        - 
        - 
        - 
        actionEnabled: 01010101
        actionIsOpen: 01010101
        actionStartIndex: 00000000070000000e00000013000000
        actionHashCodes: 3575a001fa0e540268fece05a4c8bf00
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Waypoint_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Prefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Movement/Ray
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: DidHit_b
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 00000000000000000143617253706565645f6600000000016d617853706565645f660000803f0000000000014d6f766553706565645f660000000000000000000000000000000000000000014d6f766553706565645f660ad7233c0000014469644869745f620100014469644869745f6246494e495348454401
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 070000000f0000000f0000000f0000000f000000170000000100000014000000130000001c000000110000000f0000000f00000017000000140000001200000012000000110000000100000011000000170000001700000001000000
        paramName:
        - mode
        - fromFloat
        - toFloat
        - time
        - storeResult
        - finishEvent
        - realTime
        - gameObject
        - targetObject
        - targetPosition
        - ignoreVertical
        - maxSpeed
        - finishDistance
        - finishEvent
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        - boolVariable
        - isTrue
        - isFalse
        - everyFrame
        paramDataPos: 000000000400000013000000220000002700000037000000370000000000000000000000380000004500000047000000570000005c0000000100000000000000010000005c0000006600000067000000710000007900000079000000
        paramByteDataSize: 040000000f0000000f0000000500000010000000000000000100000000000000000000000d000000020000001000000005000000000000000000000000000000000000000a000000010000000a000000080000000000000001000000
    - name: 'Lights ON '
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 336
        y: 720
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Brake
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.SendEvent
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: 5e69bb04
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams:
        - target: 2
          excludeSelf:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          gameObject:
            ownerOption: 0
            gameObject:
              useVariable: 0
              name: 
              tooltip: 
              showInInspector: 0
              networkSync: 0
              value: {fileID: 0}
          fsmName:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: Lights/Brakes
          sendToChildren:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          fsmComponent: {fileID: 0}
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 472f4f4e000000000000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1f000000170000000f00000001000000
        paramName:
        - eventTarget
        - sendEvent
        - delay
        - everyFrame
        paramDataPos: 00000000000000000400000009000000
        paramByteDataSize: 00000000040000000500000001000000
    - name: Brake
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 528
        y: 736
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: 'Lights OFF '
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.FloatInterpolate
        - HutongGames.PlayMaker.Actions.MoveTowards
        - HutongGames.PlayMaker.Actions.GetFsmBool
        - HutongGames.PlayMaker.Actions.GetFsmBool
        - HutongGames.PlayMaker.Actions.BoolNoneTrue
        customNames:
        - 
        - 
        - 
        - 
        - 
        actionEnabled: 0000010101
        actionIsOpen: 0101010101
        actionStartIndex: 00000000070000000e0000001300000018000000
        actionHashCodes: 3575a001fa0e540268fece0568fece0580768705
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Waypoint_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Prefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Movement/Ray
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: DidHit_b
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Movement/Ray
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: DidHit2_b
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 0000000000000000014d6f766553706565645f6600000000009a99993e00000000000143617253706565645f6600000000000000000000000000000000000000000143617253706565645f660ad7233c0000014469644869745f62010001446964486974325f620100014469644869745f620001446964486974325f6246494e4953484544000101
        arrayParamSizes: 02000000
        arrayParamTypes:
        - HutongGames.PlayMaker.FsmBool
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 070000000f0000000f0000000f0000000f000000170000000100000014000000130000001c000000110000000f0000000f00000017000000140000001200000012000000110000000100000014000000120000001200000011000000010000000c0000001100000011000000170000001100000001000000
        paramName:
        - mode
        - fromFloat
        - toFloat
        - time
        - storeResult
        - finishEvent
        - realTime
        - gameObject
        - targetObject
        - targetPosition
        - ignoreVertical
        - maxSpeed
        - finishDistance
        - finishEvent
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        - boolVariables
        - 
        - 
        - sendEvent
        - storeResult
        - everyFrame
        paramDataPos: 000000000400000014000000190000001e0000002d0000002d00000000000000000000002e0000003b0000003d0000004c00000051000000010000000000000001000000510000005b0000000200000002000000030000005c000000670000000000000068000000720000007d0000008500000087000000
        paramByteDataSize: 040000001000000005000000050000000f000000000000000100000000000000000000000d000000020000000f00000005000000000000000000000000000000000000000a000000010000000000000000000000000000000b00000001000000000000000a0000000b000000080000000200000001000000
    - name: 'Lights OFF '
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 704
        y: 720
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: 'Next Frame '
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.SendEvent
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: 5e69bb04
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams:
        - target: 2
          excludeSelf:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          gameObject:
            ownerOption: 0
            gameObject:
              useVariable: 0
              name: 
              tooltip: 
              showInInspector: 0
              networkSync: 0
              value: {fileID: 0}
          fsmName:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: Lights/Brakes
          sendToChildren:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          fsmComponent: {fileID: 0}
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 472f4f4646000000000000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1f000000170000000f00000001000000
        paramName:
        - eventTarget
        - sendEvent
        - delay
        - everyFrame
        paramDataPos: 0000000000000000050000000a000000
        paramByteDataSize: 00000000050000000500000001000000
    - name: 'Next Frame '
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 544
        y: 608
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Move
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.NextFrameEvent
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: 88611604
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 46494e4953484544
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 17000000
        paramName:
        - sendEvent
        paramDataPos: 00000000
        paramByteDataSize: 08000000
    events:
    - name: FINISHED
      isSystemEvent: 1
      isGlobal: 0
    - name: BACK
      isSystemEvent: 0
      isGlobal: 0
    - name: G/OFF
      isSystemEvent: 0
      isGlobal: 1
    - name: G/ON
      isSystemEvent: 0
      isGlobal: 1
    - name: MAX
      isSystemEvent: 0
      isGlobal: 0
    globalTransitions:
    - fsmEvent:
        name: G/ON
        isSystemEvent: 0
        isGlobal: 1
      toState: -Gas-
      linkStyle: 0
      linkConstraint: 0
      linkTarget: 0
      colorIndex: 0
    - fsmEvent:
        name: G/OFF
        isSystemEvent: 0
        isGlobal: 1
      toState: Move
      linkStyle: 0
      linkConstraint: 0
      linkTarget: 0
      colorIndex: 0
    variables:
      floatVariables:
      - useVariable: 1
        name: Acceleration_f
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      - useVariable: 1
        name: Brakes_f
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      - useVariable: 1
        name: CarSpeed_f
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      - useVariable: 1
        name: MoveSpeed_f
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      - useVariable: 1
        name: maxSpeed_f
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      intVariables: []
      boolVariables:
      - useVariable: 1
        name: DidHit2_b
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      - useVariable: 1
        name: DidHit_b
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      stringVariables: []
      vector2Variables: []
      vector3Variables: []
      colorVariables: []
      rectVariables: []
      quaternionVariables: []
      gameObjectVariables:
      - useVariable: 1
        name: Owner_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: Prefab_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: Waypoint_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      objectVariables:
      - useVariable: 1
        name: FL_o
        tooltip: 
        showInInspector: 0
        networkSync: 0
        typeName: UnityEngine.WheelCollider
        value: {fileID: 0}
      - useVariable: 1
        name: FR_o
        tooltip: 
        showInInspector: 0
        networkSync: 0
        typeName: UnityEngine.WheelCollider
        value: {fileID: 0}
      - useVariable: 1
        name: RL_o
        tooltip: 
        showInInspector: 0
        networkSync: 0
        typeName: UnityEngine.WheelCollider
        value: {fileID: 0}
      - useVariable: 1
        name: RR_o
        tooltip: 
        showInInspector: 0
        networkSync: 0
        typeName: UnityEngine.WheelCollider
        value: {fileID: 0}
      materialVariables: []
      textureVariables: []
      arrayVariables: []
      enumVariables: []
      categories:
      - 
      variableCategoryIDs: 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
    description: 
    docUrl: 
    showStateLabel: 0
    maxLoopCount: 0
    watermark: 
    password: 
    locked: 0
    manualUpdate: 0
    outVariableIndices: 
    keepDelayedEventsOnStateExit: 0
    preprocessed: 1
    ExposedEvents: []
    OutputEvents: []
    RestartOnEnable: 1
    ResetVariablesOnEnable: 0
    EnableDebugFlow: 0
    EnableBreakpoints: 1
    editorFlags: 2
    activeStateName: 
    mouseEvents: 0
    handleLevelLoaded: 0
    handleTriggerEnter2D: 0
    handleTriggerExit2D: 0
    handleTriggerStay2D: 0
    handleCollisionEnter2D: 0
    handleCollisionExit2D: 0
    handleCollisionStay2D: 0
    handleTriggerEnter: 0
    handleTriggerExit: 0
    handleTriggerStay: 0
    handleCollisionEnter: 0
    handleCollisionExit: 0
    handleCollisionStay: 0
    handleParticleCollision: 0
    handleControllerColliderHit: 0
    handleJointBreak: 0
    handleJointBreak2D: 0
    handleOnGUI: 0
    handleFixedUpdate: 0
    handleLateUpdate: 0
    handleApplicationEvents: 0
    handleUiEvents: 0
    handleLegacyNetworking: 0
    handleAnimatorMove: 0
    handleAnimatorIK: 0
  fsmTemplate: {fileID: 0}
  eventHandlerComponentsAdded: 1
--- !u!114 &11455448
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 108904}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1384774831, guid: de72a6d2da64d114d95e3c5a01cfaec5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fsm:
    dataVersion: 1
    usedInTemplate: {fileID: 0}
    name: Movement/Steer
    startState: owner
    states:
    - name: Get vars
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 112
        y: 240
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: -Steering-
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetFsmObject
        - HutongGames.PlayMaker.Actions.GetFsmObject
        - HutongGames.PlayMaker.Actions.GetFsmGameObject
        - HutongGames.PlayMaker.Actions.GetFsmGameObject
        customNames:
        - 
        - 
        - 
        - 
        actionEnabled: 01010101
        actionIsOpen: 01010101
        actionStartIndex: 00000000050000000a0000000f000000
        actionHashCodes: 74cb920274cb92025b66cf005b66cf00
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Prefab_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: LookAt_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams:
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: WheelCollComp_FL_o
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: WheelCollComp_FR_o
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: CarPrefab_g
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: LookAt_g
        fsmObjectParams:
        - useVariable: 1
          name: FL_o
          tooltip: 
          showInInspector: 0
          networkSync: 0
          typeName: UnityEngine.WheelCollider
          value: {fileID: 0}
        - useVariable: 1
          name: FR_o
          tooltip: 
          showInInspector: 0
          networkSync: 0
          typeName: UnityEngine.WheelCollider
          value: {fileID: 0}
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 00000000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1400000012000000120000001800000001000000140000001200000012000000180000000100000014000000120000001200000013000000010000001400000012000000120000001300000001000000
        paramName:
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        paramDataPos: 0000000000000000010000000000000000000000010000000200000003000000010000000100000002000000040000000500000000000000020000000300000006000000070000000100000003000000
        paramByteDataSize: 0000000000000000000000000000000001000000000000000000000000000000000000000100000000000000000000000000000000000000010000000000000000000000000000000000000001000000
    - name: done?
      description: 
      colorIndex: 5
      position:
        serializedVersion: 2
        x: 144
        y: 160
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Get vars
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.FsmStateTest
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: 05531600
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Owner_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: -DONE-
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 46494e4953484544000101
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 13000000120000001200000017000000170000001100000001000000
        paramName:
        - gameObject
        - fsmName
        - stateName
        - trueEvent
        - falseEvent
        - storeResult
        - everyFrame
        paramDataPos: 0000000000000000010000000000000008000000080000000a000000
        paramByteDataSize: 00000000000000000000000008000000000000000200000001000000
    - name: owner
      description: 
      colorIndex: 5
      position:
        serializedVersion: 2
        x: 144
        y: 112
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: done?
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetOwner
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: 5c004505
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Owner_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 13000000
        paramName:
        - storeGameObject
        paramDataPos: 00000000
        paramByteDataSize: 00000000
    - name: -NextFrame-
      description: 
      colorIndex: 4
      position:
        serializedVersion: 2
        x: 384
        y: 352
        width: 102
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: -Steering-
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.NextFrameEvent
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: 88611604
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 46494e4953484544
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 17000000
        paramName:
        - sendEvent
        paramDataPos: 00000000
        paramByteDataSize: 08000000
    - name: Compare rotation Left or Right
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 96
        y: 512
        width: 212
        height: 48
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: RIGHT
          isSystemEvent: 0
          isGlobal: 0
        toState: Set steering angle
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      - fsmEvent:
          name: LEFT
          isSystemEvent: 0
          isGlobal: 0
        toState: Subtract to negative value
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.FloatCompare
        - HutongGames.PlayMaker.Actions.FloatCompare
        customNames:
        - 
        - 
        actionEnabled: 0101
        actionIsOpen: 0101
        actionStartIndex: 0000000007000000
        actionHashCodes: 1bcdca041bcdca04
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 000000000161786973585f660000b442000000344300524947485400000000000161786973585f66000087430000003443004c45465400
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 0f0000000f0000000f000000170000001700000017000000010000000f0000000f0000000f00000017000000170000001700000001000000
        paramName:
        - float1
        - float2
        - tolerance
        - equal
        - lessThan
        - greaterThan
        - everyFrame
        - float1
        - float2
        - tolerance
        - equal
        - lessThan
        - greaterThan
        - everyFrame
        paramDataPos: 000000000c00000011000000160000001b0000001b0000001b0000001c000000280000002d00000032000000360000003600000036000000
        paramByteDataSize: 0c0000000500000005000000050000000000000000000000010000000c000000050000000500000004000000000000000000000001000000
    - name: Set steering angle
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 368
        y: 576
        width: 132
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: -NextFrame-
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.FloatClamp
        - HutongGames.PlayMaker.Actions.SetProperty
        - HutongGames.PlayMaker.Actions.SetProperty
        customNames:
        - 
        - 
        - 
        actionEnabled: 010101
        actionIsOpen: 010101
        actionStartIndex: 000000000400000006000000
        actionHashCodes: 0c8bc1005ac852045ac85204
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams:
        - TargetObject:
            useVariable: 1
            name: FL_o
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: UnityEngine.WheelCollider
            value: {fileID: 0}
          TargetTypeName: UnityEngine.WheelCollider
          PropertyName: steerAngle
          BoolParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          FloatParameter:
            useVariable: 1
            name: axisX_f
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          IntParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          GameObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
          StringParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 
          Vector2Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0}
          Vector3Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0}
          RectParamater:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value:
              serializedVersion: 2
              x: 0
              y: 0
              width: 0
              height: 0
          QuaternionParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0, w: 0}
          ObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: System.Single
            value: {fileID: 0}
          MaterialParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          TextureParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          ColorParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {r: 0, g: 0, b: 0, a: 1}
          EnumParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            enumName: 
            intValue: 0
          ArrayParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            type: -1
            objectTypeName: 
            floatValues: []
            intValues: 
            boolValues: 
            stringValues: []
            vector4Values: []
            objectReferences: []
          setProperty: 1
        - TargetObject:
            useVariable: 1
            name: FR_o
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: UnityEngine.WheelCollider
            value: {fileID: 0}
          TargetTypeName: UnityEngine.WheelCollider
          PropertyName: steerAngle
          BoolParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          FloatParameter:
            useVariable: 1
            name: axisX_f
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          IntParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          GameObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
          StringParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 
          Vector2Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0}
          Vector3Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0}
          RectParamater:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value:
              serializedVersion: 2
              x: 0
              y: 0
              width: 0
              height: 0
          QuaternionParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0, w: 0}
          ObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: System.Single
            value: {fileID: 0}
          MaterialParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          TextureParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          ColorParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {r: 0, g: 0, b: 0, a: 1}
          EnumParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            enumName: 
            intValue: 0
          ArrayParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            type: -1
            objectTypeName: 
            floatValues: []
            intValues: 
            boolValues: 
            stringValues: []
            vector4Values: []
            objectReferences: []
          setProperty: 1
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 000000000161786973585f66000048c2000000484200000000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 0f0000000f0000000f0000000100000024000000010000002400000001000000
        paramName:
        - floatVariable
        - minValue
        - maxValue
        - everyFrame
        - targetProperty
        - everyFrame
        - targetProperty
        - everyFrame
        paramDataPos: 000000000c000000110000001600000000000000170000000100000018000000
        paramByteDataSize: 0c00000005000000050000000100000000000000010000000000000001000000
    - name: Subtract to negative value
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 80
        y: 576
        width: 182
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Set steering angle
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.FloatSubtract
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: 2a6e8a00
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 000000000161786973585f660000b443000000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 0f0000000f0000000100000001000000
        paramName:
        - floatVariable
        - subtract
        - everyFrame
        - perSecond
        paramDataPos: 000000000c0000001100000012000000
        paramByteDataSize: 0c000000050000000100000001000000
    - name: -Steering-
      description: 'Movement/Distanced Fsm waits for this name at start.

'
      colorIndex: 4
      position:
        serializedVersion: 2
        x: 64
        y: 368
        width: 224
        height: 48
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: NO
          isSystemEvent: 0
          isGlobal: 0
        toState: Set steering straight
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Compare rotation Left or Right
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetRotation
        - HutongGames.PlayMaker.Actions.FloatCompare
        customNames:
        - 
        - 
        actionEnabled: 0101
        actionIsOpen: 0101
        actionStartIndex: 0000000008000000
        actionHashCodes: 6ab449041bcdca04
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: LookAt_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 0000000000000000000000000000000001000000000000000000000000010000000001000000000161786973585f6600000000010100000000000000000161786973585f66000000000000000040004e4f46494e495348454446494e495348454400
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 14000000230000001c0000000f0000000f0000000f00000007000000010000000f0000000f0000000f00000017000000170000001700000001000000
        paramName:
        - gameObject
        - quaternion
        - vector
        - xAngle
        - yAngle
        - zAngle
        - space
        - everyFrame
        - float1
        - float2
        - tolerance
        - equal
        - lessThan
        - greaterThan
        - everyFrame
        paramDataPos: 0000000000000000110000001e000000230000002f000000340000003800000039000000450000004a0000004f000000510000005900000061000000
        paramByteDataSize: 00000000110000000d000000050000000c0000000500000004000000010000000c000000050000000500000002000000080000000800000001000000
    - name: Set steering straight
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 384
        y: 432
        width: 147
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: -NextFrame-
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.SetProperty
        - HutongGames.PlayMaker.Actions.SetProperty
        customNames:
        - 
        - 
        actionEnabled: 0101
        actionIsOpen: 0101
        actionStartIndex: 0000000002000000
        actionHashCodes: 5ac852045ac85204
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams:
        - TargetObject:
            useVariable: 1
            name: FL_o
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: UnityEngine.WheelCollider
            value: {fileID: 0}
          TargetTypeName: UnityEngine.WheelCollider
          PropertyName: steerAngle
          BoolParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          FloatParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          IntParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          GameObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
          StringParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 
          Vector2Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0}
          Vector3Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0}
          RectParamater:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value:
              serializedVersion: 2
              x: 0
              y: 0
              width: 0
              height: 0
          QuaternionParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0, w: 0}
          ObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: System.Single
            value: {fileID: 0}
          MaterialParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          TextureParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          ColorParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {r: 0, g: 0, b: 0, a: 1}
          EnumParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            enumName: 
            intValue: 0
          ArrayParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            type: -1
            objectTypeName: 
            floatValues: []
            intValues: 
            boolValues: 
            stringValues: []
            vector4Values: []
            objectReferences: []
          setProperty: 1
        - TargetObject:
            useVariable: 1
            name: FR_o
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: UnityEngine.WheelCollider
            value: {fileID: 0}
          TargetTypeName: UnityEngine.WheelCollider
          PropertyName: steerAngle
          BoolParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          FloatParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          IntParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          GameObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
          StringParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 
          Vector2Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0}
          Vector3Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0}
          RectParamater:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value:
              serializedVersion: 2
              x: 0
              y: 0
              width: 0
              height: 0
          QuaternionParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0, w: 0}
          ObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: System.Single
            value: {fileID: 0}
          MaterialParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          TextureParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          ColorParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {r: 0, g: 0, b: 0, a: 1}
          EnumParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            enumName: 
            intValue: 0
          ArrayParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            type: -1
            objectTypeName: 
            floatValues: []
            intValues: 
            boolValues: 
            stringValues: []
            vector4Values: []
            objectReferences: []
          setProperty: 1
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 0000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 24000000010000002400000001000000
        paramName:
        - targetProperty
        - everyFrame
        - targetProperty
        - everyFrame
        paramDataPos: 00000000000000000100000001000000
        paramByteDataSize: 00000000010000000000000001000000
    - name: Smooth Look
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 128
        y: 752
        width: 103
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Dont turn if car is not moving
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetGameObjectSpeed
        - HutongGames.PlayMaker.Actions.FloatDivide
        - HutongGames.PlayMaker.Actions.FloatCompare
        - HutongGames.PlayMaker.Actions.SmoothLookAt
        customNames:
        - 
        - 
        - 
        - 
        actionEnabled: 01010101
        actionIsOpen: 01010101
        actionStartIndex: 0000000004000000070000000e000000
        actionHashCodes: f96398019845b3051bcdca044e661301
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Waypoint_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Prefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Prefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 0000000001536d6f6f74684c6f6f6b53706565645f6600000000000000000000000001000000000000000001536d6f6f74684c6f6f6b53706565645f660000604000010000000001536d6f6f74684c6f6f6b53706565645f66cdcccc3e00000000000046494e495348454446494e495348454401000000000000000000000000010000000000000000000000000100000000000001536d6f6f74684c6f6f6b53706565645f6600000ad7233c00
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 140000000f0000001c000000070000000f0000000f000000010000000f0000000f0000000f0000001700000017000000170000000100000014000000130000001c0000001c000000110000000f000000110000000f00000017000000
        paramName:
        - gameObject
        - speed
        - speedVector
        - space
        - floatVariable
        - divideBy
        - everyFrame
        - float1
        - float2
        - tolerance
        - equal
        - lessThan
        - greaterThan
        - everyFrame
        - gameObject
        - targetObject
        - targetPosition
        - upVector
        - keepVertical
        - speed
        - debug
        - finishTolerance
        - finishEvent
        paramDataPos: 00000000000000001600000023000000270000003d0000004200000043000000590000005e000000630000006b0000007300000073000000010000000000000074000000810000008e00000090000000a6000000a8000000ad000000
        paramByteDataSize: 00000000160000000d000000040000001600000005000000010000001600000005000000050000000800000008000000000000000100000000000000000000000d0000000d0000000200000016000000020000000500000000000000
    - name: NextFrame
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 272
        y: 736
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Smooth Look
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.NextFrameEvent
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: 88611604
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 46494e4953484544
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 17000000
        paramName:
        - sendEvent
        paramDataPos: 00000000
        paramByteDataSize: 08000000
    - name: Dont turn if car is not moving
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 112
        y: 832
        width: 207
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: NextFrame
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetGameObjectSpeed
        - HutongGames.PlayMaker.Actions.FloatDivide
        - HutongGames.PlayMaker.Actions.FloatCompare
        customNames:
        - 
        - 
        - 
        actionEnabled: 010101
        actionIsOpen: 010101
        actionStartIndex: 000000000400000007000000
        actionHashCodes: f96398019845b3051bcdca04
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Prefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 0000000001536d6f6f74684c6f6f6b53706565645f6600000000000000000000000001000000000000000001536d6f6f74684c6f6f6b53706565645f660000604000010000000001536d6f6f74684c6f6f6b53706565645f660000003f00000000000046494e495348454446494e495348454401
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 140000000f0000001c000000070000000f0000000f000000010000000f0000000f0000000f00000017000000170000001700000001000000
        paramName:
        - gameObject
        - speed
        - speedVector
        - space
        - floatVariable
        - divideBy
        - everyFrame
        - float1
        - float2
        - tolerance
        - equal
        - lessThan
        - greaterThan
        - everyFrame
        paramDataPos: 00000000000000001600000023000000270000003d0000004200000043000000590000005e000000630000006b0000006b00000073000000
        paramByteDataSize: 00000000160000000d0000000400000016000000050000000100000016000000050000000500000008000000000000000800000001000000
    events:
    - name: FINISHED
      isSystemEvent: 1
      isGlobal: 0
    - name: G/OFF
      isSystemEvent: 0
      isGlobal: 1
    - name: G/ON
      isSystemEvent: 0
      isGlobal: 1
    - name: LEFT
      isSystemEvent: 0
      isGlobal: 0
    - name: NO
      isSystemEvent: 0
      isGlobal: 0
    - name: RIGHT
      isSystemEvent: 0
      isGlobal: 0
    globalTransitions:
    - fsmEvent:
        name: G/ON
        isSystemEvent: 0
        isGlobal: 1
      toState: -Steering-
      linkStyle: 0
      linkConstraint: 0
      linkTarget: 0
      colorIndex: 0
    - fsmEvent:
        name: G/OFF
        isSystemEvent: 0
        isGlobal: 1
      toState: Smooth Look
      linkStyle: 0
      linkConstraint: 0
      linkTarget: 0
      colorIndex: 0
    variables:
      floatVariables:
      - useVariable: 1
        name: SmoothLookSpeed_f
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      - useVariable: 1
        name: axisX_f
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      intVariables: []
      boolVariables: []
      stringVariables: []
      vector2Variables: []
      vector3Variables: []
      colorVariables: []
      rectVariables: []
      quaternionVariables: []
      gameObjectVariables:
      - useVariable: 1
        name: LookAt_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: Owner_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: Prefab_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: Waypoint_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      objectVariables:
      - useVariable: 1
        name: FL_o
        tooltip: 
        showInInspector: 0
        networkSync: 0
        typeName: UnityEngine.WheelCollider
        value: {fileID: 0}
      - useVariable: 1
        name: FR_o
        tooltip: 
        showInInspector: 0
        networkSync: 0
        typeName: UnityEngine.WheelCollider
        value: {fileID: 0}
      materialVariables: []
      textureVariables: []
      arrayVariables: []
      enumVariables: []
      categories:
      - 
      variableCategoryIDs: 0000000000000000000000000000000000000000000000000000000000000000
    description: 
    docUrl: 
    showStateLabel: 0
    maxLoopCount: 0
    watermark: 
    password: 
    locked: 0
    manualUpdate: 0
    outVariableIndices: 
    keepDelayedEventsOnStateExit: 0
    preprocessed: 1
    ExposedEvents: []
    OutputEvents: []
    RestartOnEnable: 1
    ResetVariablesOnEnable: 0
    EnableDebugFlow: 0
    EnableBreakpoints: 1
    editorFlags: 2
    activeStateName: 
    mouseEvents: 0
    handleLevelLoaded: 0
    handleTriggerEnter2D: 0
    handleTriggerExit2D: 0
    handleTriggerStay2D: 0
    handleCollisionEnter2D: 0
    handleCollisionExit2D: 0
    handleCollisionStay2D: 0
    handleTriggerEnter: 0
    handleTriggerExit: 0
    handleTriggerStay: 0
    handleCollisionEnter: 0
    handleCollisionExit: 0
    handleCollisionStay: 0
    handleParticleCollision: 0
    handleControllerColliderHit: 0
    handleJointBreak: 0
    handleJointBreak2D: 0
    handleOnGUI: 0
    handleFixedUpdate: 0
    handleLateUpdate: 1
    handleApplicationEvents: 0
    handleUiEvents: 0
    handleLegacyNetworking: 0
    handleAnimatorMove: 0
    handleAnimatorIK: 0
  fsmTemplate: {fileID: 0}
  eventHandlerComponentsAdded: 1
--- !u!114 &11469472
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 108904}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1384774831, guid: de72a6d2da64d114d95e3c5a01cfaec5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fsm:
    dataVersion: 1
    usedInTemplate: {fileID: 0}
    name: Wheels/Turn
    startState: owner
    states:
    - name: GET Mesh
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 64
        y: 336
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: MeshRotate
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetFsmGameObject
        - HutongGames.PlayMaker.Actions.GetFsmGameObject
        - HutongGames.PlayMaker.Actions.GetFsmGameObject
        - HutongGames.PlayMaker.Actions.GetFsmGameObject
        customNames:
        - 
        - 
        - 
        - 
        actionEnabled: 01010101
        actionIsOpen: 01010101
        actionStartIndex: 00000000050000000a0000000f000000
        actionHashCodes: 5b66cf005b66cf005b66cf005b66cf00
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Mesh_FL_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: Mesh_FR_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: Mesh_RL_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: Mesh_RR_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams:
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: WheelMesh_FL_g
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: WheelMesh_FR_g
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: WheelMesh_RL_g
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: WheelMesh_RR_g
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 00000000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1400000012000000120000001300000001000000140000001200000012000000130000000100000014000000120000001200000013000000010000001400000012000000120000001300000001000000
        paramName:
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        paramDataPos: 0000000000000000010000000000000000000000010000000200000003000000010000000100000002000000040000000500000002000000020000000300000006000000070000000300000003000000
        paramByteDataSize: 0000000000000000000000000000000001000000000000000000000000000000000000000100000000000000000000000000000000000000010000000000000000000000000000000000000001000000
    - name: done?
      description: 
      colorIndex: 5
      position:
        serializedVersion: 2
        x: 64
        y: 176
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: GET Coll
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.FsmStateTest
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: 05531600
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Owner_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: -DONE-
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 46494e4953484544000101
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 13000000120000001200000017000000170000001100000001000000
        paramName:
        - gameObject
        - fsmName
        - stateName
        - trueEvent
        - falseEvent
        - storeResult
        - everyFrame
        paramDataPos: 0000000000000000010000000000000008000000080000000a000000
        paramByteDataSize: 00000000000000000000000008000000000000000200000001000000
    - name: owner
      description: 
      colorIndex: 5
      position:
        serializedVersion: 2
        x: 64
        y: 112
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: done?
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetOwner
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: 5c004505
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Owner_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 13000000
        paramName:
        - storeGameObject
        paramDataPos: 00000000
        paramByteDataSize: 00000000
    - name: GET Coll
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 64
        y: 272
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: GET Mesh
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetFsmObject
        - HutongGames.PlayMaker.Actions.GetFsmObject
        - HutongGames.PlayMaker.Actions.GetFsmObject
        - HutongGames.PlayMaker.Actions.GetFsmObject
        customNames:
        - 
        - 
        - 
        - 
        actionEnabled: 01010101
        actionIsOpen: 01010101
        actionStartIndex: 00000000050000000a0000000f000000
        actionHashCodes: 74cb920274cb920274cb920274cb9202
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams:
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: WheelCollComp_FL_o
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: WheelCollComp_FR_o
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: WheelCollComp_RL_o
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: WheelCollComp_RR_o
        fsmObjectParams:
        - useVariable: 1
          name: FL_o
          tooltip: 
          showInInspector: 0
          networkSync: 0
          typeName: UnityEngine.WheelCollider
          value: {fileID: 0}
        - useVariable: 1
          name: FR_o
          tooltip: 
          showInInspector: 0
          networkSync: 0
          typeName: UnityEngine.WheelCollider
          value: {fileID: 0}
        - useVariable: 1
          name: RL_o
          tooltip: 
          showInInspector: 0
          networkSync: 0
          typeName: UnityEngine.WheelCollider
          value: {fileID: 0}
        - useVariable: 1
          name: RR_o
          tooltip: 
          showInInspector: 0
          networkSync: 0
          typeName: UnityEngine.WheelCollider
          value: {fileID: 0}
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 00000000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1400000012000000120000001800000001000000140000001200000012000000180000000100000014000000120000001200000018000000010000001400000012000000120000001800000001000000
        paramName:
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        paramDataPos: 0000000000000000010000000000000000000000010000000200000003000000010000000100000002000000040000000500000002000000020000000300000006000000070000000300000003000000
        paramByteDataSize: 0000000000000000000000000000000001000000000000000000000000000000000000000100000000000000000000000000000000000000010000000000000000000000000000000000000001000000
    - name: MeshRotate
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 64
        y: 448
        width: 100
        height: 16
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions: []
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetProperty
        - HutongGames.PlayMaker.Actions.GetProperty
        - HutongGames.PlayMaker.Actions.GetProperty
        - HutongGames.PlayMaker.Actions.GetProperty
        - HutongGames.PlayMaker.Actions.FloatMultiply
        - HutongGames.PlayMaker.Actions.FloatMultiply
        - HutongGames.PlayMaker.Actions.FloatMultiply
        - HutongGames.PlayMaker.Actions.FloatMultiply
        - HutongGames.PlayMaker.Actions.Rotate
        - HutongGames.PlayMaker.Actions.Rotate
        - HutongGames.PlayMaker.Actions.Rotate
        - HutongGames.PlayMaker.Actions.Rotate
        customNames:
        - 
        - 
        - 
        - 
        - 
        - 
        - 
        - 
        - 
        - 
        - 
        - 
        actionEnabled: 010101010101010101010101
        actionIsOpen: 010101010101010100000000
        actionStartIndex: 00000000020000000400000006000000080000000b0000000e00000011000000140000001e0000002800000032000000
        actionHashCodes: 5ac852045ac852045ac852045ac852049845b3059845b3059845b3059845b305b0327400b0327400b0327400b0327400
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Mesh_FL_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Mesh_FR_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Mesh_RL_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Mesh_RR_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams:
        - TargetObject:
            useVariable: 1
            name: FL_o
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: UnityEngine.WheelCollider
            value: {fileID: 0}
          TargetTypeName: UnityEngine.WheelCollider
          PropertyName: rpm
          BoolParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          FloatParameter:
            useVariable: 1
            name: rpm_FL_f
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          IntParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          GameObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
          StringParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 
          Vector2Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0}
          Vector3Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0}
          RectParamater:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value:
              serializedVersion: 2
              x: 0
              y: 0
              width: 0
              height: 0
          QuaternionParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0, w: 0}
          ObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: System.Single
            value: {fileID: 0}
          MaterialParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          TextureParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          ColorParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {r: 0, g: 0, b: 0, a: 1}
          EnumParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            enumName: 
            intValue: 0
          ArrayParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            type: -1
            objectTypeName: 
            floatValues: []
            intValues: 
            boolValues: 
            stringValues: []
            vector4Values: []
            objectReferences: []
          setProperty: 0
        - TargetObject:
            useVariable: 1
            name: FR_o
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: UnityEngine.WheelCollider
            value: {fileID: 0}
          TargetTypeName: UnityEngine.WheelCollider
          PropertyName: rpm
          BoolParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          FloatParameter:
            useVariable: 1
            name: rpm_FR_f
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          IntParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          GameObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
          StringParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 
          Vector2Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0}
          Vector3Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0}
          RectParamater:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value:
              serializedVersion: 2
              x: 0
              y: 0
              width: 0
              height: 0
          QuaternionParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0, w: 0}
          ObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: System.Single
            value: {fileID: 0}
          MaterialParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          TextureParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          ColorParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {r: 0, g: 0, b: 0, a: 1}
          EnumParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            enumName: 
            intValue: 0
          ArrayParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            type: -1
            objectTypeName: 
            floatValues: []
            intValues: 
            boolValues: 
            stringValues: []
            vector4Values: []
            objectReferences: []
          setProperty: 0
        - TargetObject:
            useVariable: 1
            name: RL_o
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: UnityEngine.WheelCollider
            value: {fileID: 0}
          TargetTypeName: UnityEngine.WheelCollider
          PropertyName: rpm
          BoolParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          FloatParameter:
            useVariable: 1
            name: rpm_RL_f
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          IntParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          GameObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
          StringParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 
          Vector2Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0}
          Vector3Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0}
          RectParamater:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value:
              serializedVersion: 2
              x: 0
              y: 0
              width: 0
              height: 0
          QuaternionParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0, w: 0}
          ObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: System.Single
            value: {fileID: 0}
          MaterialParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          TextureParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          ColorParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {r: 0, g: 0, b: 0, a: 1}
          EnumParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            enumName: 
            intValue: 0
          ArrayParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            type: -1
            objectTypeName: 
            floatValues: []
            intValues: 
            boolValues: 
            stringValues: []
            vector4Values: []
            objectReferences: []
          setProperty: 0
        - TargetObject:
            useVariable: 1
            name: RR_o
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: UnityEngine.WheelCollider
            value: {fileID: 0}
          TargetTypeName: UnityEngine.WheelCollider
          PropertyName: rpm
          BoolParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          FloatParameter:
            useVariable: 1
            name: rpm_RR_f
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          IntParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          GameObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
          StringParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 
          Vector2Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0}
          Vector3Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0}
          RectParamater:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value:
              serializedVersion: 2
              x: 0
              y: 0
              width: 0
              height: 0
          QuaternionParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0, w: 0}
          ObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: System.Single
            value: {fileID: 0}
          MaterialParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          TextureParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          ColorParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {r: 0, g: 0, b: 0, a: 1}
          EnumParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            enumName: 
            intValue: 0
          ArrayParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            type: -1
            objectTypeName: 
            floatValues: []
            intValues: 
            boolValues: 
            stringValues: []
            vector4Values: []
            objectReferences: []
          setProperty: 0
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 01010101000000000172706d5f464c5f66666666400001000000000172706d5f46525f66666666400001000000000172706d5f524c5f66666666400001000000000172706d5f52525f6666666640000100000000000000000000000001000000000172706d5f464c5f6600000000010000000001010000000101000000000000000000000000000001000000000172706d5f46525f6600000000010000000001010000000101000000000000000000000000000001000000000172706d5f524c5f6600000000010000000001010000000101000000000000000000000000000001000000000172706d5f52525f66000000000100000000010100000001010000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 24000000010000002400000001000000240000000100000024000000010000000f0000000f000000010000000f0000000f000000010000000f0000000f000000010000000f0000000f00000001000000140000001c0000000f0000000f0000000f0000000700000001000000010000000100000001000000140000001c0000000f0000000f0000000f0000000700000001000000010000000100000001000000140000001c0000000f0000000f0000000f0000000700000001000000010000000100000001000000140000001c0000000f0000000f0000000f0000000700000001000000010000000100000001000000
        paramName:
        - targetProperty
        - everyFrame
        - targetProperty
        - everyFrame
        - targetProperty
        - everyFrame
        - targetProperty
        - everyFrame
        - floatVariable
        - multiplyBy
        - everyFrame
        - floatVariable
        - multiplyBy
        - everyFrame
        - floatVariable
        - multiplyBy
        - everyFrame
        - floatVariable
        - multiplyBy
        - everyFrame
        - gameObject
        - vector
        - xAngle
        - yAngle
        - zAngle
        - space
        - perSecond
        - everyFrame
        - lateUpdate
        - fixedUpdate
        - gameObject
        - vector
        - xAngle
        - yAngle
        - zAngle
        - space
        - perSecond
        - everyFrame
        - lateUpdate
        - fixedUpdate
        - gameObject
        - vector
        - xAngle
        - yAngle
        - zAngle
        - space
        - perSecond
        - everyFrame
        - lateUpdate
        - fixedUpdate
        - gameObject
        - vector
        - xAngle
        - yAngle
        - zAngle
        - space
        - perSecond
        - everyFrame
        - lateUpdate
        - fixedUpdate
        paramDataPos: 00000000000000000100000001000000020000000200000003000000030000000400000011000000160000001700000024000000290000002a000000370000003c0000003d0000004a0000004f00000000000000500000005d0000006a0000006f0000007400000078000000790000007a0000007b000000010000007c00000089000000960000009b000000a0000000a4000000a5000000a6000000a700000002000000a8000000b5000000c2000000c7000000cc000000d0000000d1000000d2000000d300000003000000d4000000e1000000ee000000f3000000f8000000fc000000fd000000fe000000ff000000
        paramByteDataSize: 00000000010000000000000001000000000000000100000000000000010000000d00000005000000010000000d00000005000000010000000d00000005000000010000000d0000000500000001000000000000000d0000000d00000005000000050000000400000001000000010000000100000001000000000000000d0000000d00000005000000050000000400000001000000010000000100000001000000000000000d0000000d00000005000000050000000400000001000000010000000100000001000000000000000d0000000d00000005000000050000000400000001000000010000000100000001000000
    events:
    - name: FINISHED
      isSystemEvent: 1
      isGlobal: 0
    globalTransitions: []
    variables:
      floatVariables:
      - useVariable: 1
        name: rpm_FL_f
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      - useVariable: 1
        name: rpm_FR_f
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      - useVariable: 1
        name: rpm_RL_f
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      - useVariable: 1
        name: rpm_RR_f
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      intVariables: []
      boolVariables: []
      stringVariables: []
      vector2Variables: []
      vector3Variables: []
      colorVariables: []
      rectVariables: []
      quaternionVariables: []
      gameObjectVariables:
      - useVariable: 1
        name: Mesh_FL_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: Mesh_FR_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: Mesh_RL_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: Mesh_RR_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: Owner_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      objectVariables:
      - useVariable: 1
        name: FL_o
        tooltip: 
        showInInspector: 0
        networkSync: 0
        typeName: UnityEngine.WheelCollider
        value: {fileID: 0}
      - useVariable: 1
        name: FR_o
        tooltip: 
        showInInspector: 0
        networkSync: 0
        typeName: UnityEngine.WheelCollider
        value: {fileID: 0}
      - useVariable: 1
        name: RL_o
        tooltip: 
        showInInspector: 0
        networkSync: 0
        typeName: UnityEngine.WheelCollider
        value: {fileID: 0}
      - useVariable: 1
        name: RR_o
        tooltip: 
        showInInspector: 0
        networkSync: 0
        typeName: UnityEngine.WheelCollider
        value: {fileID: 0}
      materialVariables: []
      textureVariables: []
      arrayVariables: []
      enumVariables: []
      categories:
      - 
      variableCategoryIDs: 00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
    description: 
    docUrl: 
    showStateLabel: 0
    maxLoopCount: 0
    watermark: 
    password: 
    locked: 0
    manualUpdate: 0
    outVariableIndices: 
    keepDelayedEventsOnStateExit: 0
    preprocessed: 1
    ExposedEvents: []
    OutputEvents: []
    RestartOnEnable: 0
    ResetVariablesOnEnable: 0
    EnableDebugFlow: 0
    EnableBreakpoints: 1
    editorFlags: 2
    activeStateName: 
    mouseEvents: 0
    handleLevelLoaded: 0
    handleTriggerEnter2D: 0
    handleTriggerExit2D: 0
    handleTriggerStay2D: 0
    handleCollisionEnter2D: 0
    handleCollisionExit2D: 0
    handleCollisionStay2D: 0
    handleTriggerEnter: 0
    handleTriggerExit: 0
    handleTriggerStay: 0
    handleCollisionEnter: 0
    handleCollisionExit: 0
    handleCollisionStay: 0
    handleParticleCollision: 0
    handleControllerColliderHit: 0
    handleJointBreak: 0
    handleJointBreak2D: 0
    handleOnGUI: 0
    handleFixedUpdate: 0
    handleLateUpdate: 0
    handleApplicationEvents: 0
    handleUiEvents: 0
    handleLegacyNetworking: 0
    handleAnimatorMove: 0
    handleAnimatorIK: 0
  fsmTemplate: {fileID: 0}
  eventHandlerComponentsAdded: 1
--- !u!114 &11486228
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 108904}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1384774831, guid: de72a6d2da64d114d95e3c5a01cfaec5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fsm:
    dataVersion: 1
    usedInTemplate: {fileID: 0}
    name: Wheels/Steer
    startState: owner
    states:
    - name: done?
      description: 
      colorIndex: 5
      position:
        serializedVersion: 2
        x: 64
        y: 176
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: GET
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.FsmStateTest
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: 05531600
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Owner_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: -DONE-
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 46494e4953484544000101
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 13000000120000001200000017000000170000001100000001000000
        paramName:
        - gameObject
        - fsmName
        - stateName
        - trueEvent
        - falseEvent
        - storeResult
        - everyFrame
        paramDataPos: 0000000000000000010000000000000008000000080000000a000000
        paramByteDataSize: 00000000000000000000000008000000000000000200000001000000
    - name: owner
      description: 
      colorIndex: 5
      position:
        serializedVersion: 2
        x: 64
        y: 112
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: done?
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetOwner
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: 5c004505
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Owner_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 13000000
        paramName:
        - storeGameObject
        paramDataPos: 00000000
        paramByteDataSize: 00000000
    - name: GET
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 64
        y: 240
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: MeshSteer
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetFsmObject
        - HutongGames.PlayMaker.Actions.GetFsmGameObject
        - HutongGames.PlayMaker.Actions.GetFsmGameObject
        customNames:
        - 
        - 
        - 
        actionEnabled: 010101
        actionIsOpen: 010101
        actionStartIndex: 00000000050000000a000000
        actionHashCodes: 74cb92025b66cf005b66cf00
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: MeshEmpty_FL_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: MeshEmpty_FR_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams:
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: WheelCollComp_FL_o
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: WheelMeshEmpty_FL_g
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: WheelMeshEmpty_FR_g
        fsmObjectParams:
        - useVariable: 1
          name: FL_o
          tooltip: 
          showInInspector: 0
          networkSync: 0
          typeName: UnityEngine.WheelCollider
          value: {fileID: 0}
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 000000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 140000001200000012000000180000000100000014000000120000001200000013000000010000001400000012000000120000001300000001000000
        paramName:
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        paramDataPos: 000000000000000001000000000000000000000001000000020000000300000000000000010000000200000004000000050000000100000002000000
        paramByteDataSize: 000000000000000000000000000000000100000000000000000000000000000000000000010000000000000000000000000000000000000001000000
    - name: MeshSteer
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 64
        y: 352
        width: 100
        height: 16
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions: []
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetProperty
        - HutongGames.PlayMaker.Actions.FloatMultiply
        - HutongGames.PlayMaker.Actions.SetRotation
        - HutongGames.PlayMaker.Actions.SetRotation
        customNames:
        - 
        - 
        - 
        - 
        actionEnabled: 01010101
        actionIsOpen: 01010101
        actionStartIndex: 0000000002000000050000000e000000
        actionHashCodes: 5ac852049845b305979e4104979e4104
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: MeshEmpty_FL_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: MeshEmpty_FR_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams:
        - TargetObject:
            useVariable: 1
            name: FL_o
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: UnityEngine.WheelCollider
            value: {fileID: 0}
          TargetTypeName: UnityEngine.WheelCollider
          PropertyName: steerAngle
          BoolParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          FloatParameter:
            useVariable: 1
            name: CollSteerAngle_f
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          IntParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          GameObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
          StringParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 
          Vector2Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0}
          Vector3Parameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0}
          RectParamater:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value:
              serializedVersion: 2
              x: 0
              y: 0
              width: 0
              height: 0
          QuaternionParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {x: 0, y: 0, z: 0, w: 0}
          ObjectParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: System.Single
            value: {fileID: 0}
          MaterialParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          TextureParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            typeName: 
            value: {fileID: 0}
          ColorParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {r: 0, g: 0, b: 0, a: 1}
          EnumParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            enumName: 
            intValue: 0
          ArrayParameter:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            type: -1
            objectTypeName: 
            floatValues: []
            intValues: 
            boolValues: 
            stringValues: []
            vector4Values: []
            objectReferences: []
          setProperty: 0
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 010000000001436f6c6c5374656572416e676c655f666666a63f000100000000000000000000000000000000010000000000000000000000000100000000010000000001436f6c6c5374656572416e676c655f66000000000101000000010000000000000000000000000000000000010000000000000000000000000100000000010000000001436f6c6c5374656572416e676c655f660000000001010000000100
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 24000000010000000f0000000f0000000100000014000000230000001c0000000f0000000f0000000f00000007000000010000000100000014000000230000001c0000000f0000000f0000000f000000070000000100000001000000
        paramName:
        - targetProperty
        - everyFrame
        - floatVariable
        - multiplyBy
        - everyFrame
        - gameObject
        - quaternion
        - vector
        - xAngle
        - yAngle
        - zAngle
        - space
        - everyFrame
        - lateUpdate
        - gameObject
        - quaternion
        - vector
        - xAngle
        - yAngle
        - zAngle
        - space
        - everyFrame
        - lateUpdate
        paramDataPos: 000000000000000001000000160000001b000000000000001c0000002d0000003a0000003f00000054000000590000005d0000005e000000010000005f000000700000007d00000082000000970000009c000000a0000000a1000000
        paramByteDataSize: 000000000100000015000000050000000100000000000000110000000d00000005000000150000000500000004000000010000000100000000000000110000000d000000050000001500000005000000040000000100000001000000
    events:
    - name: FINISHED
      isSystemEvent: 1
      isGlobal: 0
    globalTransitions: []
    variables:
      floatVariables:
      - useVariable: 1
        name: CollSteerAngle_f
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      intVariables: []
      boolVariables: []
      stringVariables: []
      vector2Variables: []
      vector3Variables: []
      colorVariables: []
      rectVariables: []
      quaternionVariables: []
      gameObjectVariables:
      - useVariable: 1
        name: MeshEmpty_FL_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: MeshEmpty_FR_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: Owner_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      objectVariables:
      - useVariable: 1
        name: FL_o
        tooltip: 
        showInInspector: 0
        networkSync: 0
        typeName: UnityEngine.WheelCollider
        value: {fileID: 0}
      materialVariables: []
      textureVariables: []
      arrayVariables: []
      enumVariables: []
      categories:
      - 
      variableCategoryIDs: 0000000000000000000000000000000000000000
    description: 
    docUrl: 
    showStateLabel: 0
    maxLoopCount: 0
    watermark: 
    password: 
    locked: 0
    manualUpdate: 0
    outVariableIndices: 
    keepDelayedEventsOnStateExit: 0
    preprocessed: 1
    ExposedEvents: []
    OutputEvents: []
    RestartOnEnable: 0
    ResetVariablesOnEnable: 0
    EnableDebugFlow: 0
    EnableBreakpoints: 1
    editorFlags: 2
    activeStateName: 
    mouseEvents: 0
    handleLevelLoaded: 0
    handleTriggerEnter2D: 0
    handleTriggerExit2D: 0
    handleTriggerStay2D: 0
    handleCollisionEnter2D: 0
    handleCollisionExit2D: 0
    handleCollisionStay2D: 0
    handleTriggerEnter: 0
    handleTriggerExit: 0
    handleTriggerStay: 0
    handleCollisionEnter: 0
    handleCollisionExit: 0
    handleCollisionStay: 0
    handleParticleCollision: 0
    handleControllerColliderHit: 0
    handleJointBreak: 0
    handleJointBreak2D: 0
    handleOnGUI: 0
    handleFixedUpdate: 0
    handleLateUpdate: 0
    handleApplicationEvents: 0
    handleUiEvents: 0
    handleLegacyNetworking: 0
    handleAnimatorMove: 0
    handleAnimatorIK: 0
  fsmTemplate: {fileID: 0}
  eventHandlerComponentsAdded: 1
--- !u!114 &11498232
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 108904}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1384774831, guid: de72a6d2da64d114d95e3c5a01cfaec5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fsm:
    dataVersion: 1
    usedInTemplate: {fileID: 0}
    name: Movement/Waypoints
    startState: Done?
    states:
    - name: Done?
      description: 
      colorIndex: 5
      position:
        serializedVersion: 2
        x: 576
        y: 112
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Ray done?
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetOwner
        - HutongGames.PlayMaker.Actions.FsmStateTest
        customNames:
        - 
        - 
        actionEnabled: 0101
        actionIsOpen: 0101
        actionStartIndex: 0000000001000000
        actionHashCodes: 5c00450505531600
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Owner_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: Owner_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: -DONE-
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 46494e4953484544000101
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1300000013000000120000001200000017000000170000001100000001000000
        paramName:
        - storeGameObject
        - gameObject
        - fsmName
        - stateName
        - trueEvent
        - falseEvent
        - storeResult
        - everyFrame
        paramDataPos: 000000000100000000000000010000000000000008000000080000000a000000
        paramByteDataSize: 0000000000000000000000000000000008000000000000000200000001000000
    - name: GET
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 576
        y: 240
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: SET
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetFsmGameObject
        - HutongGames.PlayMaker.Actions.GetFsmGameObject
        customNames:
        - 
        - 
        actionEnabled: 0101
        actionIsOpen: 0101
        actionStartIndex: 0000000005000000
        actionHashCodes: 5b66cf005b66cf00
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Prefab_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: LookAt_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams:
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: CarPrefab_g
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: LookAt_g
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 0000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 14000000120000001200000013000000010000001400000012000000120000001300000001000000
        paramName:
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        paramDataPos: 00000000000000000100000000000000000000000100000002000000030000000100000001000000
        paramByteDataSize: 00000000000000000000000000000000010000000000000000000000000000000000000001000000
    - name: SET
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 576
        y: 288
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Wait for first WP_Group
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.SetFloatValue
        - HutongGames.PlayMaker.Actions.SetFsmFloat
        - HutongGames.PlayMaker.Actions.GetFsmFloat
        - HutongGames.PlayMaker.Actions.FloatOperator
        customNames:
        - 
        - 
        - 
        - 
        actionEnabled: 01010000
        actionIsOpen: 01010101
        actionStartIndex: 0000000003000000080000000d000000
        actionHashCodes: 9845b30587604502876045020cdb0405
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams:
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Movement/Distanced
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: MinDistToWP_Default_f
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Movement/Ray
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Distance_f
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 000000000144697374616e63655f4d696e5f746f576179706f696e745f660000c0400000000000000144697374616e63655f4d696e5f746f576179706f696e745f6600000000000152617944697374616e63655f44656661756c745f6600000000000152617944697374616e63655f44656661756c745f666666e63f0003000000000000000152617944697374616e63655f4e65775f6600
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 0f0000000f000000010000001400000012000000120000000f000000010000001400000012000000120000000f000000010000000f0000000f000000070000000f00000001000000
        paramName:
        - floatVariable
        - floatValue
        - everyFrame
        - gameObject
        - fsmName
        - variableName
        - setValue
        - everyFrame
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        - float1
        - float2
        - operation
        - storeResult
        - everyFrame
        paramDataPos: 000000001e000000230000000000000000000000010000002400000042000000010000000200000003000000430000005d0000005e000000780000007d0000008100000097000000
        paramByteDataSize: 1e00000005000000010000000000000000000000000000001e000000010000000000000000000000000000001a000000010000001a00000005000000040000001600000001000000
    - name: Closest
      description: 'Reset values and finds closest starting waypoint.

'
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 800
        y: 496
        width: 201.5
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Random Direction
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.SetIntValue
        - HutongGames.PlayMaker.Actions.SetIntValue
        - HutongGames.PlayMaker.Actions.SetBoolValue
        - HutongGames.PlayMaker.Actions.ArrayListGetClosestGameObject
        customNames:
        - 
        - 
        - 
        - 
        actionEnabled: 01010101
        actionIsOpen: 01010101
        actionStartIndex: 00000000030000000600000009000000
        actionHashCodes: 603dd905603dd90527fbe103b7d9a300
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Prefab_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: Direction_Next_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: WP_Group_Next_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Directions
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 0000000001576179706f696e745f696e6465785f690000000000000000000001576179706f696e74735f546f74616c5f69000000000000000157505f697346697273745f6201000000000000000000000000000000000000000001
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1000000010000000010000001000000010000000010000001100000011000000010000001400000012000000130000001c000000010000001300000010000000
        paramName:
        - intVariable
        - intValue
        - everyFrame
        - intVariable
        - intValue
        - everyFrame
        - boolVariable
        - boolValue
        - everyFrame
        - gameObject
        - reference
        - distanceFrom
        - orDistanceFromVector3
        - everyframe
        - closestGameObject
        - closestIndex
        paramDataPos: 00000000150000001a0000001b000000310000003600000037000000450000004700000000000000000000000000000048000000550000000100000056000000
        paramByteDataSize: 1500000005000000010000001600000005000000010000000e00000002000000010000000000000000000000000000000d000000010000000000000005000000
    - name: Random Direction
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 560
        y: 512
        width: 132
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: is it Left or Right or other
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetRandomChild
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: d97b7202
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Waypoint_Now_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Direction_Next_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1400000013000000
        paramName:
        - gameObject
        - storeResult
        paramDataPos: 0000000000000000
        paramByteDataSize: 0000000000000000
    - name: Set WP to steering LookAt
      description: '+ /Drive

        + /Steer

'
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 512
        y: 864
        width: 182
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Check Distance
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.SetFsmGameObject
        - HutongGames.PlayMaker.Actions.SetFsmGameObject
        - HutongGames.PlayMaker.Actions.SetFsmGameObject
        customNames:
        - 
        - 
        - 
        actionEnabled: 010101
        actionIsOpen: 010101
        actionStartIndex: 00000000050000000a000000
        actionHashCodes: 5b66cf005b66cf005b66cf00
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Waypoint_Next_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: Waypoint_Next_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: Waypoint_Next_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams:
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Movement/LookAt
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Waypoint_g
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Movement/Drive
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Waypoint_g
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Movement/Steer
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Waypoint_g
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 000000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 140000001200000012000000130000000100000014000000120000001200000013000000010000001400000012000000120000001300000001000000
        paramName:
        - gameObject
        - fsmName
        - variableName
        - setValue
        - everyFrame
        - gameObject
        - fsmName
        - variableName
        - setValue
        - everyFrame
        - gameObject
        - fsmName
        - variableName
        - setValue
        - everyFrame
        paramDataPos: 000000000000000001000000000000000000000001000000020000000300000001000000010000000200000004000000050000000200000002000000
        paramByteDataSize: 000000000000000000000000000000000100000000000000000000000000000000000000010000000000000000000000000000000000000001000000
    - name: Check Distance
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 736
        y: 880
        width: 116
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: is Max index
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetDistance
        - HutongGames.PlayMaker.Actions.FloatCompare
        customNames:
        - 
        - 
        actionEnabled: 0101
        actionIsOpen: 0101
        actionStartIndex: 0000000004000000
        actionHashCodes: 36f094001bcdca04
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Waypoint_Next_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Prefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 000000000144697374616e63655f4e6f775f6601000000000144697374616e63655f4e6f775f66000000000144697374616e63655f4d696e5f746f576179706f696e745f66000000000046494e495348454401
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 14000000130000000f000000010000000f0000000f0000000f00000017000000170000001700000001000000
        paramName:
        - gameObject
        - target
        - storeResult
        - everyFrame
        - float1
        - float2
        - tolerance
        - equal
        - lessThan
        - greaterThan
        - everyFrame
        paramDataPos: 000000000000000000000000130000001400000027000000450000004a0000004a0000005200000052000000
        paramByteDataSize: 00000000000000001300000001000000130000001e0000000500000000000000080000000000000001000000
    - name: How much WP
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 560
        y: 720
        width: 107
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Set as Next
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetChildCount
        - HutongGames.PlayMaker.Actions.IntSubtract
        customNames:
        - 
        - 
        actionEnabled: 0101
        actionIsOpen: 0101
        actionStartIndex: 0000000003000000
        actionHashCodes: 76b3ca0531e5da02
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Waypoint_Now_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 0000000001576179706f696e74735f546f74616c5f69000000000001576179706f696e74735f546f74616c5f6901000000000000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 14000000100000000100000010000000100000000100000001000000
        paramName:
        - gameObject
        - storeResult
        - everyFrame
        - intVariable
        - subtract
        - everyFrame
        - perSecond
        paramDataPos: 000000000000000016000000170000002d0000003200000033000000
        paramByteDataSize: 00000000160000000100000016000000050000000100000001000000
    - name: Get Next WP
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 544
        y: 960
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: 'is it Left or Right or other '
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetChildNum
        - HutongGames.PlayMaker.Actions.SetBoolValue
        customNames:
        - 
        - 
        actionEnabled: 0101
        actionIsOpen: 0101
        actionStartIndex: 0000000003000000
        actionHashCodes: 280cdd0527fbe103
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Waypoint_Next_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Waypoint_Now_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 0000000001576179706f696e745f696e6465785f69000157505f697346697273745f62000000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 140000001000000013000000110000001100000001000000
        paramName:
        - gameObject
        - childIndex
        - store
        - boolVariable
        - boolValue
        - everyFrame
        paramDataPos: 000000000000000000000000150000002300000025000000
        paramByteDataSize: 0000000015000000000000000e0000000200000001000000
    - name: Set as Next
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 560
        y: 768
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Set WP to steering LookAt
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.SetGameObject
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: 4d51cf04
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Waypoint_Next_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: Waypoint_Now_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 00
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 130000001300000001000000
        paramName:
        - variable
        - gameObject
        - everyFrame
        paramDataPos: 000000000100000000000000
        paramByteDataSize: 000000000000000001000000
    - name: is Max index
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 752
        y: 944
        width: 100
        height: 64
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Get Next WP
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      - fsmEvent:
          name: NEXT
          isSystemEvent: 0
          isGlobal: 0
        toState: Add index
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      - fsmEvent:
          name: MAX
          isSystemEvent: 0
          isGlobal: 0
        toState: Raycast forward for Next WP_Group
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.BoolTest
        - HutongGames.PlayMaker.Actions.IntCompare
        customNames:
        - 
        - 
        actionEnabled: 0101
        actionIsOpen: 0101
        actionStartIndex: 0000000004000000
        actionHashCodes: a4c8bf0009faa102
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 000157505f697346697273745f6246494e4953484544000000000001576179706f696e745f696e6465785f690000000001576179706f696e74735f546f74616c5f694d41584e4558544d415800
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 11000000170000001700000001000000100000001000000017000000170000001700000001000000
        paramName:
        - boolVariable
        - isTrue
        - isFalse
        - everyFrame
        - integer1
        - integer2
        - equal
        - lessThan
        - greaterThan
        - everyFrame
        paramDataPos: 000000000e0000001600000016000000170000002c0000004200000045000000490000004c000000
        paramByteDataSize: 0e000000080000000000000001000000150000001600000003000000040000000300000001000000
    - name: Add index
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 576
        y: 1008
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Get Next WP
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.IntAdd
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: 603dd905
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 0000000001576179706f696e745f696e6465785f69010000000000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 100000001000000001000000
        paramName:
        - intVariable
        - add
        - everyFrame
        paramDataPos: 00000000150000001a000000
        paramByteDataSize: 150000000500000001000000
    - name: Raycast forward for Next WP_Group
      description: 
      colorIndex: 7
      position:
        serializedVersion: 2
        x: 976
        y: 912
        width: 239
        height: 48
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Was indicator lights on?
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      - fsmEvent:
          name: NO
          isSystemEvent: 0
          isGlobal: 0
        toState: Next Frame for loop
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.Raycast
        - HutongGames.PlayMaker.Actions.BoolTest
        customNames:
        - 
        - 
        actionEnabled: 0101
        actionIsOpen: 0101
        actionStartIndex: 0000000011000000
        actionHashCodes: d3bd7b05a4c8bf00
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: WP_Group_Next_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: LookAt_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 00000000000000000000803f0000000000000000000000803f00010000000000204200000144696446696e645f62000000000000000000000000010000000000000000000000000100000000010000000000080000000000000000803feceb6b3f8180803c0000803f000000000144696446696e645f6246494e49534845444e4f00
        arrayParamSizes: 01000000
        arrayParamTypes:
        - HutongGames.PlayMaker.FsmInt
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 140000001c0000001c000000070000000f0000001700000011000000130000001c0000001c0000000f000000100000000c0000001000000011000000190000001100000011000000170000001700000001000000
        paramName:
        - fromGameObject
        - fromPosition
        - direction
        - space
        - distance
        - hitEvent
        - storeDidHit
        - storeHitObject
        - storeHitPoint
        - storeHitNormal
        - storeHitDistance
        - repeatInterval
        - layerMask
        - 
        - invertMask
        - debugColor
        - debug
        - boolVariable
        - isTrue
        - isFalse
        - everyFrame
        paramDataPos: 00000000000000000d0000001a0000001e0000002300000023000000000000002e0000003b000000480000004d000000000000005200000057000000590000006a0000006c000000770000007f00000081000000
        paramByteDataSize: 000000000d0000000d0000000400000005000000000000000b000000000000000d0000000d000000050000000500000000000000050000000200000011000000020000000b000000080000000200000001000000
    - name: Next Frame for loop
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 1008
        y: 976
        width: 144
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Raycast forward for Next WP_Group
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.NextFrameEvent
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: 88611604
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 46494e4953484544
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 17000000
        paramName:
        - sendEvent
        paramDataPos: 00000000
        paramByteDataSize: 08000000
    - name: is it Left or Right or other
      description: "if contains \"Left\" or \"Right\"\nTurn On a Indicator Light
        \n"
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 528
        y: 576
        width: 184
        height: 80
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: LEFT
          isSystemEvent: 0
          isGlobal: 0
        toState: LEFT On
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      - fsmEvent:
          name: RIGHT
          isSystemEvent: 0
          isGlobal: 0
        toState: RIGHT On
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      - fsmEvent:
          name: NO
          isSystemEvent: 0
          isGlobal: 0
        toState: Shorter Ray distance
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: How much WP
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetName
        - HutongGames.PlayMaker.Actions.StringContains
        - HutongGames.PlayMaker.Actions.StringContains
        - HutongGames.PlayMaker.Actions.StringContains
        customNames:
        - 
        - 
        - 
        - 
        actionEnabled: 01010101
        actionIsOpen: 01010101
        actionStartIndex: 0000000003000000090000000f000000
        actionHashCodes: 71c75a030c0e55020c0e55020c0e5502
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Waypoint_Now_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 1
          name: FirstWP_Name_s
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: 
        - useVariable: 1
          name: FirstWP_Name_s
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: 
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Left
        - useVariable: 1
          name: FirstWP_Name_s
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: 
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Right
        - useVariable: 1
          name: FirstWP_Name_s
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: 
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Circle
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 004c45465400010052494748540001004e4f46494e4953484544000100
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 130000001200000001000000120000001200000017000000170000001100000001000000120000001200000017000000170000001100000001000000120000001200000017000000170000001100000001000000
        paramName:
        - gameObject
        - storeName
        - everyFrame
        - stringVariable
        - containsString
        - trueEvent
        - falseEvent
        - storeResult
        - everyFrame
        - stringVariable
        - containsString
        - trueEvent
        - falseEvent
        - storeResult
        - everyFrame
        - stringVariable
        - containsString
        - trueEvent
        - falseEvent
        - storeResult
        - everyFrame
        paramDataPos: 0000000000000000000000000100000002000000010000000500000005000000070000000300000004000000080000000d0000000d0000000f000000050000000600000010000000120000001a0000001c000000
        paramByteDataSize: 000000000000000001000000000000000000000004000000000000000200000001000000000000000000000005000000000000000200000001000000000000000000000002000000080000000200000001000000
    - name: LEFT On
      description: "When turning Left, the Tag is changed so it will not trigger
        a Stopper for coming traffic from opposite direction. \n"
      colorIndex: 2
      position:
        serializedVersion: 2
        x: 64
        y: 560
        width: 397
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: How much WP
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.SendEvent
        - HutongGames.PlayMaker.Actions.SetBoolValue
        - HutongGames.PlayMaker.Actions.SetTag
        customNames:
        - 
        - 
        - 
        actionEnabled: 010101
        actionIsOpen: 010101
        actionStartIndex: 000000000400000007000000
        actionHashCodes: 5e69bb0427fbe1039bd6f801
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Prefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams:
        - target: 2
          excludeSelf:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          gameObject:
            ownerOption: 0
            gameObject:
              useVariable: 0
              name: 
              tooltip: 
              showInInspector: 0
              networkSync: 0
              value: {fileID: 0}
          fsmName:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: Lights/Left
          sendToChildren:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          fsmComponent: {fileID: 0}
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Finish
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 472f4f4e000000000000000169734c6566744f6e5f62010000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1f000000170000000f000000010000001100000011000000010000001400000012000000
        paramName:
        - eventTarget
        - sendEvent
        - delay
        - everyFrame
        - boolVariable
        - boolValue
        - everyFrame
        - gameObject
        - tag
        paramDataPos: 000000000000000004000000090000000a00000016000000180000000000000000000000
        paramByteDataSize: 000000000400000005000000010000000c00000002000000010000000000000000000000
    - name: RIGHT On
      description: 
      colorIndex: 2
      position:
        serializedVersion: 2
        x: 272
        y: 656
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: How much WP
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.SendEvent
        - HutongGames.PlayMaker.Actions.SetBoolValue
        customNames:
        - 
        - 
        actionEnabled: 0101
        actionIsOpen: 0101
        actionStartIndex: 0000000004000000
        actionHashCodes: 5e69bb0427fbe103
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams:
        - target: 2
          excludeSelf:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          gameObject:
            ownerOption: 0
            gameObject:
              useVariable: 0
              name: 
              tooltip: 
              showInInspector: 0
              networkSync: 0
              value: {fileID: 0}
          fsmName:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: Lights/Right
          sendToChildren:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          fsmComponent: {fileID: 0}
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 472f4f4e0000000000000001697352696768744f6e5f62010000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1f000000170000000f00000001000000110000001100000001000000
        paramName:
        - eventTarget
        - sendEvent
        - delay
        - everyFrame
        - boolVariable
        - boolValue
        - everyFrame
        paramDataPos: 000000000000000004000000090000000a0000001700000019000000
        paramByteDataSize: 000000000400000005000000010000000d0000000200000001000000
    - name: Was indicator lights on?
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 1056
        y: 656
        width: 170
        height: 80
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Closest
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      - fsmEvent:
          name: LEFT
          isSystemEvent: 0
          isGlobal: 0
        toState: LEFT Off
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      - fsmEvent:
          name: RIGHT
          isSystemEvent: 0
          isGlobal: 0
        toState: RIGHT Off
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      - fsmEvent:
          name: YES
          isSystemEvent: 0
          isGlobal: 1
        toState: Default Ray distance
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.BoolTest
        - HutongGames.PlayMaker.Actions.BoolTest
        - HutongGames.PlayMaker.Actions.BoolTest
        customNames:
        - 
        - 
        - 
        actionEnabled: 010101
        actionIsOpen: 010101
        actionStartIndex: 000000000400000008000000
        actionHashCodes: a4c8bf00a4c8bf00a4c8bf00
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 000169734c6566744f6e5f624c454654000001697352696768744f6e5f6252494748540000016973436972636c655261795f6259455346494e495348454400
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 110000001700000017000000010000001100000017000000170000000100000011000000170000001700000001000000
        paramName:
        - boolVariable
        - isTrue
        - isFalse
        - everyFrame
        - boolVariable
        - isTrue
        - isFalse
        - everyFrame
        - boolVariable
        - isTrue
        - isFalse
        - everyFrame
        paramDataPos: 000000000c0000001000000010000000110000001e00000023000000230000002400000033000000360000003e000000
        paramByteDataSize: 0c0000000400000000000000010000000d0000000500000000000000010000000f000000030000000800000001000000
    - name: LEFT Off
      description: 
      colorIndex: 2
      position:
        serializedVersion: 2
        x: 1312
        y: 528
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Closest
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.SendEvent
        - HutongGames.PlayMaker.Actions.SetBoolValue
        - HutongGames.PlayMaker.Actions.SetTag
        customNames:
        - 
        - 
        - 
        actionEnabled: 010101
        actionIsOpen: 010101
        actionStartIndex: 000000000400000007000000
        actionHashCodes: 5e69bb0427fbe1039bd6f801
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Prefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams:
        - target: 2
          excludeSelf:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          gameObject:
            ownerOption: 0
            gameObject:
              useVariable: 0
              name: 
              tooltip: 
              showInInspector: 0
              networkSync: 0
              value: {fileID: 0}
          fsmName:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: Lights/Left
          sendToChildren:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          fsmComponent: {fileID: 0}
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Car
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 472f4f4646000000000000000169734c6566744f6e5f62000000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1f000000170000000f000000010000001100000011000000010000001400000012000000
        paramName:
        - eventTarget
        - sendEvent
        - delay
        - everyFrame
        - boolVariable
        - boolValue
        - everyFrame
        - gameObject
        - tag
        paramDataPos: 0000000000000000050000000a0000000b00000017000000190000000000000000000000
        paramByteDataSize: 000000000500000005000000010000000c00000002000000010000000000000000000000
    - name: RIGHT Off
      description: 
      colorIndex: 2
      position:
        serializedVersion: 2
        x: 1312
        y: 576
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Closest
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.SendEvent
        - HutongGames.PlayMaker.Actions.SetBoolValue
        customNames:
        - 
        - 
        actionEnabled: 0101
        actionIsOpen: 0101
        actionStartIndex: 0000000004000000
        actionHashCodes: 5e69bb0427fbe103
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams:
        - target: 2
          excludeSelf:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          gameObject:
            ownerOption: 0
            gameObject:
              useVariable: 0
              name: 
              tooltip: 
              showInInspector: 0
              networkSync: 0
              value: {fileID: 0}
          fsmName:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: Lights/Right
          sendToChildren:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          fsmComponent: {fileID: 0}
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 472f4f46460000000000000001697352696768744f6e5f62000000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1f000000170000000f00000001000000110000001100000001000000
        paramName:
        - eventTarget
        - sendEvent
        - delay
        - everyFrame
        - boolVariable
        - boolValue
        - everyFrame
        paramDataPos: 0000000000000000050000000a0000000b000000180000001a000000
        paramByteDataSize: 000000000500000005000000010000000d0000000200000001000000
    - name: Wait for first WP_Group
      description: 'this is set by "AI_Cars" spawner in the scene.

'
      colorIndex: 5
      position:
        serializedVersion: 2
        x: 784
        y: 304
        width: 232.5
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Closest
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GameObjectIsNull
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: 15656205
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: WP_Group_Next_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 46494e4953484544000101
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1300000017000000170000001100000001000000
        paramName:
        - gameObject
        - isNull
        - isNotNull
        - storeResult
        - everyFrame
        paramDataPos: 000000000000000000000000080000000a000000
        paramByteDataSize: 0000000000000000080000000200000001000000
    - name: Shorter Ray distance
      description: 'This helps to get cars pass circle intersections.

'
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 784
        y: 656
        width: 229.5
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: How much WP
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetFsmFloat
        - HutongGames.PlayMaker.Actions.FloatOperator
        - HutongGames.PlayMaker.Actions.SetFsmFloat
        - HutongGames.PlayMaker.Actions.SetBoolValue
        customNames:
        - 
        - 
        - 
        - 
        actionEnabled: 01010101
        actionIsOpen: 01010101
        actionStartIndex: 00000000050000000a0000000f000000
        actionHashCodes: 876045020cdb04058760450227fbe103
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams:
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Movement/Ray
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Distance_f
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Movement/Ray
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Distance_f
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 000000000152617944697374616e63655f44656661756c745f6600000000000152617944697374616e63655f44656661756c745f666666e63f0003000000000000000152617944697374616e63655f4e65775f6600000000000152617944697374616e63655f4e65775f660000016973436972636c655261795f62010000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1400000012000000120000000f000000010000000f0000000f000000070000000f000000010000001400000012000000120000000f00000001000000110000001100000001000000
        paramName:
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        - float1
        - float2
        - operation
        - storeResult
        - everyFrame
        - gameObject
        - fsmName
        - variableName
        - setValue
        - everyFrame
        - boolVariable
        - boolValue
        - everyFrame
        paramDataPos: 000000000000000001000000000000001a0000001b000000350000003a0000003e00000054000000010000000200000003000000550000006b0000006c0000007b0000007d000000
        paramByteDataSize: 0000000000000000000000001a000000010000001a0000000500000004000000160000000100000000000000000000000000000016000000010000000f0000000200000001000000
    - name: Default Ray distance
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 1056
        y: 752
        width: 148
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Closest
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.SetFsmFloat
        - HutongGames.PlayMaker.Actions.SetBoolValue
        customNames:
        - 
        - 
        actionEnabled: 0101
        actionIsOpen: 0101
        actionStartIndex: 0000000005000000
        actionHashCodes: 8760450227fbe103
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams:
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Movement/Ray
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Distance_f
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 000000000152617944697374616e63655f44656661756c745f660000016973436972636c655261795f62000000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1400000012000000120000000f00000001000000110000001100000001000000
        paramName:
        - gameObject
        - fsmName
        - variableName
        - setValue
        - everyFrame
        - boolVariable
        - boolValue
        - everyFrame
        paramDataPos: 000000000000000001000000000000001a0000001b0000002a0000002c000000
        paramByteDataSize: 0000000000000000000000001a000000010000000f0000000200000001000000
    - name: 'is it Left or Right or other '
      description: "if contains \"Left\" or \"Right\"\nTurn On a Indicator Light
        \n"
      colorIndex: 2
      position:
        serializedVersion: 2
        x: 176
        y: 976
        width: 184
        height: 64
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: LEFT
          isSystemEvent: 0
          isGlobal: 0
        toState: 'LEFT On '
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      - fsmEvent:
          name: RIGHT
          isSystemEvent: 0
          isGlobal: 0
        toState: 'RIGHT On '
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Set WP to steering LookAt
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetName
        - HutongGames.PlayMaker.Actions.StringContains
        - HutongGames.PlayMaker.Actions.StringContains
        customNames:
        - 
        - 
        - 
        actionEnabled: 010101
        actionIsOpen: 010101
        actionStartIndex: 000000000300000009000000
        actionHashCodes: 71c75a030c0e55020c0e5502
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Waypoint_Next_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 1
          name: NextWP_Name_s
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: 
        - useVariable: 1
          name: NextWP_Name_s
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: 
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Left
        - useVariable: 1
          name: NextWP_Name_s
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: 
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Right
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 004c454654000100524947485446494e4953484544000100
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 130000001200000001000000120000001200000017000000170000001100000001000000120000001200000017000000170000001100000001000000
        paramName:
        - gameObject
        - storeName
        - everyFrame
        - stringVariable
        - containsString
        - trueEvent
        - falseEvent
        - storeResult
        - everyFrame
        - stringVariable
        - containsString
        - trueEvent
        - falseEvent
        - storeResult
        - everyFrame
        paramDataPos: 0000000000000000000000000100000002000000010000000500000005000000070000000300000004000000080000000d0000001500000017000000
        paramByteDataSize: 000000000000000001000000000000000000000004000000000000000200000001000000000000000000000005000000080000000200000001000000
    - name: 'LEFT On '
      description: 
      colorIndex: 2
      position:
        serializedVersion: 2
        x: 144
        y: 848
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Set WP to steering LookAt
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.SendEvent
        - HutongGames.PlayMaker.Actions.SetBoolValue
        - HutongGames.PlayMaker.Actions.SetTag
        customNames:
        - 
        - 
        - 
        actionEnabled: 010101
        actionIsOpen: 010101
        actionStartIndex: 000000000400000007000000
        actionHashCodes: 5e69bb0427fbe1039bd6f801
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Prefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams:
        - target: 2
          excludeSelf:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          gameObject:
            ownerOption: 0
            gameObject:
              useVariable: 0
              name: 
              tooltip: 
              showInInspector: 0
              networkSync: 0
              value: {fileID: 0}
          fsmName:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: Lights/Left
          sendToChildren:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          fsmComponent: {fileID: 0}
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Finish
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 472f4f4e000000000000000169734c6566744f6e5f62010000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1f000000170000000f000000010000001100000011000000010000001400000012000000
        paramName:
        - eventTarget
        - sendEvent
        - delay
        - everyFrame
        - boolVariable
        - boolValue
        - everyFrame
        - gameObject
        - tag
        paramDataPos: 000000000000000004000000090000000a00000016000000180000000000000000000000
        paramByteDataSize: 000000000400000005000000010000000c00000002000000010000000000000000000000
    - name: 'RIGHT On '
      description: 
      colorIndex: 2
      position:
        serializedVersion: 2
        x: 144
        y: 896
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Set WP to steering LookAt
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.SendEvent
        - HutongGames.PlayMaker.Actions.SetBoolValue
        customNames:
        - 
        - 
        actionEnabled: 0101
        actionIsOpen: 0101
        actionStartIndex: 0000000004000000
        actionHashCodes: 5e69bb0427fbe103
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams:
        - target: 2
          excludeSelf:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          gameObject:
            ownerOption: 0
            gameObject:
              useVariable: 0
              name: 
              tooltip: 
              showInInspector: 0
              networkSync: 0
              value: {fileID: 0}
          fsmName:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: Lights/Right
          sendToChildren:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          fsmComponent: {fileID: 0}
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 472f4f4e0000000000000001697352696768744f6e5f62010000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1f000000170000000f00000001000000110000001100000001000000
        paramName:
        - eventTarget
        - sendEvent
        - delay
        - everyFrame
        - boolVariable
        - boolValue
        - everyFrame
        paramDataPos: 000000000000000004000000090000000a0000001700000019000000
        paramByteDataSize: 000000000400000005000000010000000d0000000200000001000000
    - name: Ray done?
      description: 
      colorIndex: 5
      position:
        serializedVersion: 2
        x: 576
        y: 176
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: GET
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.FsmStateTest
        customNames:
        - 
        actionEnabled: 00
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: 05531600
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Owner_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Movement/Ray
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: -Ray-
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 46494e4953484544000101
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 13000000120000001200000017000000170000001100000001000000
        paramName:
        - gameObject
        - fsmName
        - stateName
        - trueEvent
        - falseEvent
        - storeResult
        - everyFrame
        paramDataPos: 0000000000000000010000000000000008000000080000000a000000
        paramByteDataSize: 00000000000000000000000008000000000000000200000001000000
    events:
    - name: FINISHED
      isSystemEvent: 1
      isGlobal: 0
    - name: LEFT
      isSystemEvent: 0
      isGlobal: 0
    - name: MAX
      isSystemEvent: 0
      isGlobal: 0
    - name: NEXT
      isSystemEvent: 0
      isGlobal: 0
    - name: NO
      isSystemEvent: 0
      isGlobal: 0
    - name: RIGHT
      isSystemEvent: 0
      isGlobal: 0
    - name: YES
      isSystemEvent: 0
      isGlobal: 1
    globalTransitions: []
    variables:
      floatVariables:
      - useVariable: 1
        name: Distance_Min_toWaypoint_f
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      - useVariable: 1
        name: Distance_Now_f
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      - useVariable: 1
        name: RayDistance_Default_f
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      - useVariable: 1
        name: RayDistance_New_f
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      intVariables:
      - useVariable: 1
        name: Waypoint_index_i
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      - useVariable: 1
        name: Waypoints_Total_i
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      boolVariables:
      - useVariable: 1
        name: DidFind_b
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      - useVariable: 1
        name: WP_isFirst_b
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      - useVariable: 1
        name: isCircleRay_b
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      - useVariable: 1
        name: isLeftOn_b
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      - useVariable: 1
        name: isRightOn_b
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      stringVariables:
      - useVariable: 1
        name: FirstWP_Name_s
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 
      - useVariable: 1
        name: NextWP_Name_s
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 
      vector2Variables: []
      vector3Variables: []
      colorVariables: []
      rectVariables: []
      quaternionVariables: []
      gameObjectVariables:
      - useVariable: 1
        name: Direction_Next_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: LookAt_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: Owner_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: Prefab_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: WP_Group_Next_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: Waypoint_Next_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: Waypoint_Now_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      objectVariables: []
      materialVariables: []
      textureVariables: []
      arrayVariables: []
      enumVariables: []
      categories:
      - 
      variableCategoryIDs: 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
    description: 
    docUrl: 
    showStateLabel: 0
    maxLoopCount: 0
    watermark: 
    password: 
    locked: 0
    manualUpdate: 0
    outVariableIndices: 
    keepDelayedEventsOnStateExit: 0
    preprocessed: 1
    ExposedEvents: []
    OutputEvents: []
    RestartOnEnable: 1
    ResetVariablesOnEnable: 0
    EnableDebugFlow: 0
    EnableBreakpoints: 1
    editorFlags: 2
    activeStateName: 
    mouseEvents: 0
    handleLevelLoaded: 0
    handleTriggerEnter2D: 0
    handleTriggerExit2D: 0
    handleTriggerStay2D: 0
    handleCollisionEnter2D: 0
    handleCollisionExit2D: 0
    handleCollisionStay2D: 0
    handleTriggerEnter: 0
    handleTriggerExit: 0
    handleTriggerStay: 0
    handleCollisionEnter: 0
    handleCollisionExit: 0
    handleCollisionStay: 0
    handleParticleCollision: 0
    handleControllerColliderHit: 0
    handleJointBreak: 0
    handleJointBreak2D: 0
    handleOnGUI: 0
    handleFixedUpdate: 0
    handleLateUpdate: 0
    handleApplicationEvents: 0
    handleUiEvents: 0
    handleLegacyNetworking: 0
    handleAnimatorMove: 0
    handleAnimatorIK: 0
  fsmTemplate: {fileID: 0}
  eventHandlerComponentsAdded: 1
--- !u!114 &11446606
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 108904}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1384774831, guid: de72a6d2da64d114d95e3c5a01cfaec5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fsm:
    dataVersion: 1
    usedInTemplate: {fileID: 0}
    name: Movement/LookAt
    startState: done?
    states:
    - name: LookAt
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 64
        y: 272
        width: 100
        height: 16
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions: []
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.SmoothLookAt
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: 4e661301
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Waypoint_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: LookAt_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 00000000000000000000000001000000000000000000000000010100cdcc2c4000000017b7d13800
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 14000000130000001c0000001c000000110000000f000000110000000f00000017000000
        paramName:
        - gameObject
        - targetObject
        - targetPosition
        - upVector
        - keepVertical
        - speed
        - debug
        - finishTolerance
        - finishEvent
        paramDataPos: 0000000000000000000000000d0000001a0000001c000000210000002300000028000000
        paramByteDataSize: 00000000000000000d0000000d0000000200000005000000020000000500000000000000
    - name: Get vars
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 64
        y: 192
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: LookAt
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetFsmGameObject
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: 5b66cf00
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: LookAt_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams:
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: LookAt_g
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 00
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1400000012000000120000001300000001000000
        paramName:
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        paramDataPos: 0000000000000000010000000000000000000000
        paramByteDataSize: 0000000000000000000000000000000001000000
    - name: done?
      description: 
      colorIndex: 5
      position:
        serializedVersion: 2
        x: 64
        y: 112
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Get vars
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetOwner
        - HutongGames.PlayMaker.Actions.FsmStateTest
        customNames:
        - 
        - 
        actionEnabled: 0101
        actionIsOpen: 0101
        actionStartIndex: 0000000001000000
        actionHashCodes: 5c00450505531600
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Owner_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: Owner_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: -DONE-
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 46494e4953484544000101
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1300000013000000120000001200000017000000170000001100000001000000
        paramName:
        - storeGameObject
        - gameObject
        - fsmName
        - stateName
        - trueEvent
        - falseEvent
        - storeResult
        - everyFrame
        paramDataPos: 000000000100000000000000010000000000000008000000080000000a000000
        paramByteDataSize: 0000000000000000000000000000000008000000000000000200000001000000
    events:
    - name: FINISHED
      isSystemEvent: 1
      isGlobal: 0
    - name: G/OFF
      isSystemEvent: 0
      isGlobal: 1
    - name: G/ON
      isSystemEvent: 0
      isGlobal: 1
    globalTransitions: []
    variables:
      floatVariables: []
      intVariables: []
      boolVariables: []
      stringVariables: []
      vector2Variables: []
      vector3Variables: []
      colorVariables: []
      rectVariables: []
      quaternionVariables: []
      gameObjectVariables:
      - useVariable: 1
        name: LookAt_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: Owner_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: Waypoint_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      objectVariables: []
      materialVariables: []
      textureVariables: []
      arrayVariables: []
      enumVariables: []
      categories:
      - 
      variableCategoryIDs: 000000000000000000000000
    description: 
    docUrl: 
    showStateLabel: 0
    maxLoopCount: 0
    watermark: 
    password: 
    locked: 0
    manualUpdate: 0
    outVariableIndices: 
    keepDelayedEventsOnStateExit: 0
    preprocessed: 1
    ExposedEvents: []
    OutputEvents: []
    RestartOnEnable: 1
    ResetVariablesOnEnable: 0
    EnableDebugFlow: 0
    EnableBreakpoints: 1
    editorFlags: 2
    activeStateName: 
    mouseEvents: 0
    handleLevelLoaded: 0
    handleTriggerEnter2D: 0
    handleTriggerExit2D: 0
    handleTriggerStay2D: 0
    handleCollisionEnter2D: 0
    handleCollisionExit2D: 0
    handleCollisionStay2D: 0
    handleTriggerEnter: 0
    handleTriggerExit: 0
    handleTriggerStay: 0
    handleCollisionEnter: 0
    handleCollisionExit: 0
    handleCollisionStay: 0
    handleParticleCollision: 0
    handleControllerColliderHit: 0
    handleJointBreak: 0
    handleJointBreak2D: 0
    handleOnGUI: 0
    handleFixedUpdate: 0
    handleLateUpdate: 1
    handleApplicationEvents: 0
    handleUiEvents: 0
    handleLegacyNetworking: 0
    handleAnimatorMove: 0
    handleAnimatorIK: 0
  fsmTemplate: {fileID: 0}
  eventHandlerComponentsAdded: 1
--- !u!114 &11488786
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 108904}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1384774831, guid: de72a6d2da64d114d95e3c5a01cfaec5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fsm:
    dataVersion: 1
    usedInTemplate: {fileID: 0}
    name: Movement/Ray
    startState: done?
    states:
    - name: -Ray-
      description: 'Waypoint Fsm looking for this name at start.

'
      colorIndex: 4
      position:
        serializedVersion: 2
        x: 64
        y: 256
        width: 179
        height: 16
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions: []
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.Raycast
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: d3bd7b05
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: LookAt_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 0000000000000000000000000100000000000000000000803f0001000000000000000144697374616e63655f6600014469644869745f620000000000000000000000000100000000000000000000000001000000000103000000000200000000080000000001000000803f1a61393f000000000000803f000000
        arrayParamSizes: 02000000
        arrayParamTypes:
        - HutongGames.PlayMaker.FsmInt
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 140000001c0000001c000000070000000f0000001700000011000000130000001c0000001c0000000f000000100000000c0000001000000010000000110000001900000011000000
        paramName:
        - fromGameObject
        - fromPosition
        - direction
        - space
        - distance
        - hitEvent
        - storeDidHit
        - storeHitObject
        - storeHitPoint
        - storeHitNormal
        - storeHitDistance
        - repeatInterval
        - layerMask
        - 
        - 
        - invertMask
        - debugColor
        - debug
        paramDataPos: 00000000000000000d0000001a0000001e0000002d0000002d0000000000000037000000440000005100000056000000000000005b00000060000000650000006700000078000000
        paramByteDataSize: 000000000d0000000d000000040000000f000000000000000a000000000000000d0000000d0000000500000005000000000000000500000005000000020000001100000002000000
    - name: Get vars
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 96
        y: 176
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: -Ray-
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetFsmGameObject
        - HutongGames.PlayMaker.Actions.GetFsmGameObject
        - HutongGames.PlayMaker.Actions.GetFsmGameObject
        - HutongGames.PlayMaker.Actions.SetFloatValue
        customNames:
        - 
        - 
        - 
        - 
        actionEnabled: 01000001
        actionIsOpen: 01010101
        actionStartIndex: 00000000050000000a0000000f000000
        actionHashCodes: 5b66cf005b66cf005b66cf009845b305
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: LookAt_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: Ray1_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: Ray2_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams:
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: LookAt_g
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Ray1_g
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Ray2_g
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 000000000000000144697374616e63655f66000000410000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1400000012000000120000001300000001000000140000001200000012000000130000000100000014000000120000001200000013000000010000000f0000000f00000001000000
        paramName:
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        - floatVariable
        - floatValue
        - everyFrame
        paramDataPos: 000000000000000001000000000000000000000001000000020000000300000001000000010000000200000004000000050000000200000002000000030000001200000017000000
        paramByteDataSize: 0000000000000000000000000000000001000000000000000000000000000000000000000100000000000000000000000000000000000000010000000f0000000500000001000000
    - name: done?
      description: 
      colorIndex: 5
      position:
        serializedVersion: 2
        x: 96
        y: 112
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Get vars
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetOwner
        - HutongGames.PlayMaker.Actions.FsmStateTest
        customNames:
        - 
        - 
        actionEnabled: 0101
        actionIsOpen: 0101
        actionStartIndex: 0000000001000000
        actionHashCodes: 5c00450505531600
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Owner_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: Owner_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: -DONE-
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 46494e4953484544000101
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1300000013000000120000001200000017000000170000001100000001000000
        paramName:
        - storeGameObject
        - gameObject
        - fsmName
        - stateName
        - trueEvent
        - falseEvent
        - storeResult
        - everyFrame
        paramDataPos: 000000000100000000000000010000000000000008000000080000000a000000
        paramByteDataSize: 0000000000000000000000000000000008000000000000000200000001000000
    events:
    - name: FINISHED
      isSystemEvent: 1
      isGlobal: 0
    globalTransitions: []
    variables:
      floatVariables:
      - useVariable: 1
        name: Distance_f
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      intVariables: []
      boolVariables:
      - useVariable: 1
        name: DidHit2_b
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      - useVariable: 1
        name: DidHit_b
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      stringVariables: []
      vector2Variables: []
      vector3Variables: []
      colorVariables: []
      rectVariables: []
      quaternionVariables: []
      gameObjectVariables:
      - useVariable: 1
        name: LookAt_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: Owner_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: Ray1_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: Ray2_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      objectVariables: []
      materialVariables: []
      textureVariables: []
      arrayVariables: []
      enumVariables: []
      categories:
      - 
      variableCategoryIDs: 00000000000000000000000000000000000000000000000000000000
    description: 
    docUrl: 
    showStateLabel: 0
    maxLoopCount: 0
    watermark: 
    password: 
    locked: 0
    manualUpdate: 0
    outVariableIndices: 
    keepDelayedEventsOnStateExit: 0
    preprocessed: 1
    ExposedEvents: []
    OutputEvents: []
    RestartOnEnable: 1
    ResetVariablesOnEnable: 0
    EnableDebugFlow: 0
    EnableBreakpoints: 1
    editorFlags: 2
    activeStateName: 
    mouseEvents: 0
    handleLevelLoaded: 0
    handleTriggerEnter2D: 0
    handleTriggerExit2D: 0
    handleTriggerStay2D: 0
    handleCollisionEnter2D: 0
    handleCollisionExit2D: 0
    handleCollisionStay2D: 0
    handleTriggerEnter: 0
    handleTriggerExit: 0
    handleTriggerStay: 0
    handleCollisionEnter: 0
    handleCollisionExit: 0
    handleCollisionStay: 0
    handleParticleCollision: 0
    handleControllerColliderHit: 0
    handleJointBreak: 0
    handleJointBreak2D: 0
    handleOnGUI: 0
    handleFixedUpdate: 0
    handleLateUpdate: 0
    handleApplicationEvents: 0
    handleUiEvents: 0
    handleLegacyNetworking: 0
    handleAnimatorMove: 0
    handleAnimatorIK: 0
  fsmTemplate: {fileID: 0}
  eventHandlerComponentsAdded: 1
--- !u!114 &11440224
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 108904}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1384774831, guid: de72a6d2da64d114d95e3c5a01cfaec5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fsm:
    dataVersion: 1
    usedInTemplate: {fileID: 0}
    name: Lights/Brakes
    startState: done?
    states:
    - name: GET
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 64
        y: 176
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: SET
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetFsmGameObject
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: 5b66cf00
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Brakes_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams:
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: LightsBrakes_g
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 00
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1400000012000000120000001300000001000000
        paramName:
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        paramDataPos: 0000000000000000010000000000000000000000
        paramByteDataSize: 0000000000000000000000000000000001000000
    - name: done?
      description: 
      colorIndex: 5
      position:
        serializedVersion: 2
        x: 64
        y: 112
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: GET
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetOwner
        - HutongGames.PlayMaker.Actions.FsmStateTest
        customNames:
        - 
        - 
        actionEnabled: 0101
        actionIsOpen: 0101
        actionStartIndex: 0000000001000000
        actionHashCodes: 5c00450505531600
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Owner_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: Owner_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: -DONE-
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 46494e4953484544000101
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1300000013000000120000001200000017000000170000001100000001000000
        paramName:
        - storeGameObject
        - gameObject
        - fsmName
        - stateName
        - trueEvent
        - falseEvent
        - storeResult
        - everyFrame
        paramDataPos: 000000000100000000000000010000000000000008000000080000000a000000
        paramByteDataSize: 0000000000000000000000000000000008000000000000000200000001000000
    - name: SET
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 64
        y: 224
        width: 100
        height: 16
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions: []
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.ActivateGameObject
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: b2cf1000
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Brakes_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 000000000000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1400000011000000110000000100000001000000
        paramName:
        - gameObject
        - activate
        - recursive
        - resetOnExit
        - everyFrame
        paramDataPos: 0000000000000000020000000400000005000000
        paramByteDataSize: 0000000002000000020000000100000001000000
    - name: On
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 256
        y: 224
        width: 100
        height: 16
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions: []
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.ActivateGameObject
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: b2cf1000
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Brakes_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 010000000000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1400000011000000110000000100000001000000
        paramName:
        - gameObject
        - activate
        - recursive
        - resetOnExit
        - everyFrame
        paramDataPos: 0000000000000000020000000400000005000000
        paramByteDataSize: 0000000002000000020000000100000001000000
    - name: Off
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 448
        y: 224
        width: 100
        height: 16
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions: []
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.ActivateGameObject
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: b2cf1000
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Brakes_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 000000000000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1400000011000000110000000100000001000000
        paramName:
        - gameObject
        - activate
        - recursive
        - resetOnExit
        - everyFrame
        paramDataPos: 0000000000000000020000000400000005000000
        paramByteDataSize: 0000000002000000020000000100000001000000
    events:
    - name: FINISHED
      isSystemEvent: 1
      isGlobal: 0
    - name: G/OFF
      isSystemEvent: 0
      isGlobal: 1
    - name: G/ON
      isSystemEvent: 0
      isGlobal: 1
    globalTransitions:
    - fsmEvent:
        name: G/ON
        isSystemEvent: 0
        isGlobal: 1
      toState: On
      linkStyle: 0
      linkConstraint: 0
      linkTarget: 0
      colorIndex: 0
    - fsmEvent:
        name: G/OFF
        isSystemEvent: 0
        isGlobal: 1
      toState: Off
      linkStyle: 0
      linkConstraint: 0
      linkTarget: 0
      colorIndex: 0
    variables:
      floatVariables: []
      intVariables: []
      boolVariables: []
      stringVariables: []
      vector2Variables: []
      vector3Variables: []
      colorVariables: []
      rectVariables: []
      quaternionVariables: []
      gameObjectVariables:
      - useVariable: 1
        name: Brakes_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: Owner_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      objectVariables: []
      materialVariables: []
      textureVariables: []
      arrayVariables: []
      enumVariables: []
      categories:
      - 
      variableCategoryIDs: 0000000000000000
    description: 
    docUrl: 
    showStateLabel: 0
    maxLoopCount: 0
    watermark: 
    password: 
    locked: 0
    manualUpdate: 0
    outVariableIndices: 
    keepDelayedEventsOnStateExit: 0
    preprocessed: 1
    ExposedEvents: []
    OutputEvents: []
    RestartOnEnable: 1
    ResetVariablesOnEnable: 0
    EnableDebugFlow: 0
    EnableBreakpoints: 1
    editorFlags: 2
    activeStateName: 
    mouseEvents: 0
    handleLevelLoaded: 0
    handleTriggerEnter2D: 0
    handleTriggerExit2D: 0
    handleTriggerStay2D: 0
    handleCollisionEnter2D: 0
    handleCollisionExit2D: 0
    handleCollisionStay2D: 0
    handleTriggerEnter: 0
    handleTriggerExit: 0
    handleTriggerStay: 0
    handleCollisionEnter: 0
    handleCollisionExit: 0
    handleCollisionStay: 0
    handleParticleCollision: 0
    handleControllerColliderHit: 0
    handleJointBreak: 0
    handleJointBreak2D: 0
    handleOnGUI: 0
    handleFixedUpdate: 0
    handleLateUpdate: 0
    handleApplicationEvents: 0
    handleUiEvents: 0
    handleLegacyNetworking: 0
    handleAnimatorMove: 0
    handleAnimatorIK: 0
  fsmTemplate: {fileID: 0}
  eventHandlerComponentsAdded: 1
--- !u!114 &11473998
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 108904}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1384774831, guid: de72a6d2da64d114d95e3c5a01cfaec5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fsm:
    dataVersion: 1
    usedInTemplate: {fileID: 0}
    name: Lights/Left
    startState: done?
    states:
    - name: GET
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 64
        y: 176
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: SET
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetFsmGameObject
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: 5b66cf00
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Left_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams:
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: LightsLEFT_g
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 00
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1400000012000000120000001300000001000000
        paramName:
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        paramDataPos: 0000000000000000010000000000000000000000
        paramByteDataSize: 0000000000000000000000000000000001000000
    - name: done?
      description: 
      colorIndex: 5
      position:
        serializedVersion: 2
        x: 64
        y: 112
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: GET
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetOwner
        - HutongGames.PlayMaker.Actions.FsmStateTest
        customNames:
        - 
        - 
        actionEnabled: 0101
        actionIsOpen: 0101
        actionStartIndex: 0000000001000000
        actionHashCodes: 5c00450505531600
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Owner_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: Owner_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: -DONE-
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 46494e4953484544000101
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1300000013000000120000001200000017000000170000001100000001000000
        paramName:
        - storeGameObject
        - gameObject
        - fsmName
        - stateName
        - trueEvent
        - falseEvent
        - storeResult
        - everyFrame
        paramDataPos: 000000000100000000000000010000000000000008000000080000000a000000
        paramByteDataSize: 0000000000000000000000000000000008000000000000000200000001000000
    - name: SET
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 64
        y: 224
        width: 100
        height: 16
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions: []
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.ActivateGameObject
        - HutongGames.PlayMaker.Actions.RandomFloat
        customNames:
        - 
        - 
        actionEnabled: 0101
        actionIsOpen: 0101
        actionStartIndex: 0000000005000000
        actionHashCodes: b2cf10002ee01704
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Left_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 000000000000cdcc4c3e009a99993e00000000000154696d6552616e646f6d5f66
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 14000000110000001100000001000000010000000f0000000f0000000f000000
        paramName:
        - gameObject
        - activate
        - recursive
        - resetOnExit
        - everyFrame
        - min
        - max
        - storeResult
        paramDataPos: 0000000000000000020000000400000005000000060000000b00000010000000
        paramByteDataSize: 0000000002000000020000000100000001000000050000000500000011000000
    - name: Off
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 480
        y: 224
        width: 100
        height: 16
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions: []
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.ActivateGameObject
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: b2cf1000
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Left_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 000000000000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1400000011000000110000000100000001000000
        paramName:
        - gameObject
        - activate
        - recursive
        - resetOnExit
        - everyFrame
        paramDataPos: 0000000000000000020000000400000005000000
        paramByteDataSize: 0000000002000000020000000100000001000000
    - name: On
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 272
        y: 224
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: 'Off '
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.ActivateGameObject
        - HutongGames.PlayMaker.Actions.Wait
        customNames:
        - 
        - 
        actionEnabled: 0101
        actionIsOpen: 0101
        actionStartIndex: 0000000005000000
        actionHashCodes: b2cf100048c01101
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Left_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 010000000000000000000154696d6552616e646f6d5f6646494e495348454401
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 14000000110000001100000001000000010000000f0000001700000001000000
        paramName:
        - gameObject
        - activate
        - recursive
        - resetOnExit
        - everyFrame
        - time
        - finishEvent
        - realTime
        paramDataPos: 000000000000000002000000040000000500000006000000170000001f000000
        paramByteDataSize: 0000000002000000020000000100000001000000110000000800000001000000
    - name: 'Off '
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 272
        y: 304
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: On
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.ActivateGameObject
        - HutongGames.PlayMaker.Actions.Wait
        customNames:
        - 
        - 
        actionEnabled: 0101
        actionIsOpen: 0101
        actionStartIndex: 0000000005000000
        actionHashCodes: b2cf100048c01101
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Left_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 000000000000000000000154696d6552616e646f6d5f6646494e495348454401
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 14000000110000001100000001000000010000000f0000001700000001000000
        paramName:
        - gameObject
        - activate
        - recursive
        - resetOnExit
        - everyFrame
        - time
        - finishEvent
        - realTime
        paramDataPos: 000000000000000002000000040000000500000006000000170000001f000000
        paramByteDataSize: 0000000002000000020000000100000001000000110000000800000001000000
    events:
    - name: FINISHED
      isSystemEvent: 1
      isGlobal: 0
    - name: G/ON
      isSystemEvent: 0
      isGlobal: 1
    - name: G/OFF
      isSystemEvent: 0
      isGlobal: 1
    globalTransitions:
    - fsmEvent:
        name: G/ON
        isSystemEvent: 0
        isGlobal: 1
      toState: On
      linkStyle: 0
      linkConstraint: 0
      linkTarget: 0
      colorIndex: 0
    - fsmEvent:
        name: G/OFF
        isSystemEvent: 0
        isGlobal: 1
      toState: Off
      linkStyle: 0
      linkConstraint: 0
      linkTarget: 0
      colorIndex: 0
    variables:
      floatVariables:
      - useVariable: 1
        name: TimeRandom_f
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      intVariables: []
      boolVariables: []
      stringVariables: []
      vector2Variables: []
      vector3Variables: []
      colorVariables: []
      rectVariables: []
      quaternionVariables: []
      gameObjectVariables:
      - useVariable: 1
        name: Left_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: Owner_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      objectVariables: []
      materialVariables: []
      textureVariables: []
      arrayVariables: []
      enumVariables: []
      categories:
      - 
      variableCategoryIDs: 000000000000000000000000
    description: 
    docUrl: 
    showStateLabel: 0
    maxLoopCount: 0
    watermark: 
    password: 
    locked: 0
    manualUpdate: 0
    outVariableIndices: 
    keepDelayedEventsOnStateExit: 0
    preprocessed: 1
    ExposedEvents: []
    OutputEvents: []
    RestartOnEnable: 1
    ResetVariablesOnEnable: 0
    EnableDebugFlow: 0
    EnableBreakpoints: 1
    editorFlags: 2
    activeStateName: 
    mouseEvents: 0
    handleLevelLoaded: 0
    handleTriggerEnter2D: 0
    handleTriggerExit2D: 0
    handleTriggerStay2D: 0
    handleCollisionEnter2D: 0
    handleCollisionExit2D: 0
    handleCollisionStay2D: 0
    handleTriggerEnter: 0
    handleTriggerExit: 0
    handleTriggerStay: 0
    handleCollisionEnter: 0
    handleCollisionExit: 0
    handleCollisionStay: 0
    handleParticleCollision: 0
    handleControllerColliderHit: 0
    handleJointBreak: 0
    handleJointBreak2D: 0
    handleOnGUI: 0
    handleFixedUpdate: 0
    handleLateUpdate: 0
    handleApplicationEvents: 0
    handleUiEvents: 0
    handleLegacyNetworking: 0
    handleAnimatorMove: 0
    handleAnimatorIK: 0
  fsmTemplate: {fileID: 0}
  eventHandlerComponentsAdded: 1
--- !u!114 &11441826
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 108904}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1384774831, guid: de72a6d2da64d114d95e3c5a01cfaec5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fsm:
    dataVersion: 1
    usedInTemplate: {fileID: 0}
    name: Lights/Right
    startState: done?
    states:
    - name: 'Off '
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 272
        y: 304
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: On
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.ActivateGameObject
        - HutongGames.PlayMaker.Actions.Wait
        customNames:
        - 
        - 
        actionEnabled: 0101
        actionIsOpen: 0101
        actionStartIndex: 0000000005000000
        actionHashCodes: b2cf100048c01101
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Right_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 000000000000000000000154696d6552616e646f6d5f6646494e495348454401
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 14000000110000001100000001000000010000000f0000001700000001000000
        paramName:
        - gameObject
        - activate
        - recursive
        - resetOnExit
        - everyFrame
        - time
        - finishEvent
        - realTime
        paramDataPos: 000000000000000002000000040000000500000006000000170000001f000000
        paramByteDataSize: 0000000002000000020000000100000001000000110000000800000001000000
    - name: GET
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 64
        y: 176
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: SET
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetFsmGameObject
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: 5b66cf00
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Right_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams:
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: LightsRIGHT_g
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 00
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1400000012000000120000001300000001000000
        paramName:
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        paramDataPos: 0000000000000000010000000000000000000000
        paramByteDataSize: 0000000000000000000000000000000001000000
    - name: done?
      description: 
      colorIndex: 5
      position:
        serializedVersion: 2
        x: 64
        y: 112
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: GET
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetOwner
        - HutongGames.PlayMaker.Actions.FsmStateTest
        customNames:
        - 
        - 
        actionEnabled: 0101
        actionIsOpen: 0101
        actionStartIndex: 0000000001000000
        actionHashCodes: 5c00450505531600
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Owner_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: Owner_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: -DONE-
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 46494e4953484544000101
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1300000013000000120000001200000017000000170000001100000001000000
        paramName:
        - storeGameObject
        - gameObject
        - fsmName
        - stateName
        - trueEvent
        - falseEvent
        - storeResult
        - everyFrame
        paramDataPos: 000000000100000000000000010000000000000008000000080000000a000000
        paramByteDataSize: 0000000000000000000000000000000008000000000000000200000001000000
    - name: SET
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 64
        y: 224
        width: 100
        height: 16
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions: []
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.ActivateGameObject
        - HutongGames.PlayMaker.Actions.RandomFloat
        customNames:
        - 
        - 
        actionEnabled: 0101
        actionIsOpen: 0101
        actionStartIndex: 0000000005000000
        actionHashCodes: b2cf10002ee01704
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Right_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 0000000000009a99193e009a99993e00000000000154696d6552616e646f6d5f66
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 14000000110000001100000001000000010000000f0000000f0000000f000000
        paramName:
        - gameObject
        - activate
        - recursive
        - resetOnExit
        - everyFrame
        - min
        - max
        - storeResult
        paramDataPos: 0000000000000000020000000400000005000000060000000b00000010000000
        paramByteDataSize: 0000000002000000020000000100000001000000050000000500000011000000
    - name: On
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 272
        y: 224
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: 'Off '
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.ActivateGameObject
        - HutongGames.PlayMaker.Actions.Wait
        customNames:
        - 
        - 
        actionEnabled: 0101
        actionIsOpen: 0101
        actionStartIndex: 0000000005000000
        actionHashCodes: b2cf100048c01101
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Right_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 010000000000000000000154696d6552616e646f6d5f6646494e495348454401
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 14000000110000001100000001000000010000000f0000001700000001000000
        paramName:
        - gameObject
        - activate
        - recursive
        - resetOnExit
        - everyFrame
        - time
        - finishEvent
        - realTime
        paramDataPos: 000000000000000002000000040000000500000006000000170000001f000000
        paramByteDataSize: 0000000002000000020000000100000001000000110000000800000001000000
    - name: Off
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 480
        y: 224
        width: 100
        height: 16
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions: []
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.ActivateGameObject
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: b2cf1000
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Right_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 000000000000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1400000011000000110000000100000001000000
        paramName:
        - gameObject
        - activate
        - recursive
        - resetOnExit
        - everyFrame
        paramDataPos: 0000000000000000020000000400000005000000
        paramByteDataSize: 0000000002000000020000000100000001000000
    events:
    - name: FINISHED
      isSystemEvent: 1
      isGlobal: 0
    - name: G/ON
      isSystemEvent: 0
      isGlobal: 1
    - name: G/OFF
      isSystemEvent: 0
      isGlobal: 1
    globalTransitions:
    - fsmEvent:
        name: G/ON
        isSystemEvent: 0
        isGlobal: 1
      toState: On
      linkStyle: 0
      linkConstraint: 0
      linkTarget: 0
      colorIndex: 0
    - fsmEvent:
        name: G/OFF
        isSystemEvent: 0
        isGlobal: 1
      toState: Off
      linkStyle: 0
      linkConstraint: 0
      linkTarget: 0
      colorIndex: 0
    variables:
      floatVariables:
      - useVariable: 1
        name: TimeRandom_f
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      intVariables: []
      boolVariables: []
      stringVariables: []
      vector2Variables: []
      vector3Variables: []
      colorVariables: []
      rectVariables: []
      quaternionVariables: []
      gameObjectVariables:
      - useVariable: 1
        name: Owner_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: Right_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      objectVariables: []
      materialVariables: []
      textureVariables: []
      arrayVariables: []
      enumVariables: []
      categories:
      - 
      variableCategoryIDs: 000000000000000000000000
    description: 
    docUrl: 
    showStateLabel: 0
    maxLoopCount: 0
    watermark: 
    password: 
    locked: 0
    manualUpdate: 0
    outVariableIndices: 
    keepDelayedEventsOnStateExit: 0
    preprocessed: 1
    ExposedEvents: []
    OutputEvents: []
    RestartOnEnable: 1
    ResetVariablesOnEnable: 0
    EnableDebugFlow: 0
    EnableBreakpoints: 1
    editorFlags: 2
    activeStateName: 
    mouseEvents: 0
    handleLevelLoaded: 0
    handleTriggerEnter2D: 0
    handleTriggerExit2D: 0
    handleTriggerStay2D: 0
    handleCollisionEnter2D: 0
    handleCollisionExit2D: 0
    handleCollisionStay2D: 0
    handleTriggerEnter: 0
    handleTriggerExit: 0
    handleTriggerStay: 0
    handleCollisionEnter: 0
    handleCollisionExit: 0
    handleCollisionStay: 0
    handleParticleCollision: 0
    handleControllerColliderHit: 0
    handleJointBreak: 0
    handleJointBreak2D: 0
    handleOnGUI: 0
    handleFixedUpdate: 0
    handleLateUpdate: 0
    handleApplicationEvents: 0
    handleUiEvents: 0
    handleLegacyNetworking: 0
    handleAnimatorMove: 0
    handleAnimatorIK: 0
  fsmTemplate: {fileID: 0}
  eventHandlerComponentsAdded: 1
--- !u!114 &11401864
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 108904}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1384774831, guid: de72a6d2da64d114d95e3c5a01cfaec5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fsm:
    dataVersion: 1
    usedInTemplate: {fileID: 0}
    name: Movement/Distanced
    startState: SETUP done?
    states:
    - name: is Prefab far or visible
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 80
        y: 400
        width: 157
        height: 48
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Set Kinematic mode
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      - fsmEvent:
          name: NO
          isSystemEvent: 0
          isGlobal: 0
        toState: Wait
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetDistance
        - HutongGames.PlayMaker.Actions.FloatCompare
        - HutongGames.PlayMaker.Actions.GameObjectIsVisible
        customNames:
        - 
        - 
        - 
        actionEnabled: 010101
        actionIsOpen: 010101
        actionStartIndex: 00000000040000000b000000
        actionHashCodes: 36f094001bcdca0416e9ed03
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: MainCam_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Prefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Body_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 000000000144697374616e6365546f43616d6572615f6600000000000144697374616e6365546f43616d6572615f660000704200000000000046494e4953484544004e4f46494e4953484544000100
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 14000000130000000f000000010000000f0000000f0000000f000000170000001700000017000000010000001400000017000000170000001100000001000000
        paramName:
        - gameObject
        - target
        - storeResult
        - everyFrame
        - float1
        - float2
        - tolerance
        - equal
        - lessThan
        - greaterThan
        - everyFrame
        - gameObject
        - trueEvent
        - falseEvent
        - storeResult
        - everyFrame
        paramDataPos: 00000000000000000000000017000000180000002f00000034000000390000003900000039000000410000000100000042000000440000004c0000004e000000
        paramByteDataSize: 00000000000000001700000001000000170000000500000005000000000000000000000008000000010000000000000002000000080000000200000001000000
    - name: GET SET
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 752
        y: 176
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: is Prefab far or visible
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetMainCamera
        - HutongGames.PlayMaker.Actions.GetFsmGameObject
        - HutongGames.PlayMaker.Actions.FindChild
        - HutongGames.PlayMaker.Actions.FindChild
        - HutongGames.PlayMaker.Actions.FloatOperator
        - HutongGames.PlayMaker.Actions.GetFsmFloat
        - HutongGames.PlayMaker.Actions.FloatOperator
        customNames:
        - 
        - 
        - 
        - 
        - 
        - 
        - 
        actionEnabled: 01010101010101
        actionIsOpen: 01010101010101
        actionStartIndex: 000000000100000006000000090000000c0000001100000016000000
        actionHashCodes: 5c0045055b66cf00f90ba701f90ba7010cdb0405876045020cdb0405
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: MainCam_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: Prefab_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: Body_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: PrefabWheelCollGroup_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams:
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Prefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Prefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: CarPrefab_g
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Body
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: WheelColl
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Movement/Ray
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Distance_f
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 0000000000014d696e44697374546f57505f44656661756c745f6600000040000300000000000000014d696e44697374546f57505f4e65775f6600000000000152617944697374616e63655f44656661756c745f6600000000000152617944697374616e63655f44656661756c745f669a99d93f0003000000000000000152617944697374616e63655f4e65775f6600
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1300000014000000120000001200000013000000010000001400000012000000130000001400000012000000130000000f0000000f000000070000000f000000010000001400000012000000120000000f000000010000000f0000000f000000070000000f00000001000000
        paramName:
        - storeGameObject
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        - gameObject
        - childName
        - storeResult
        - gameObject
        - childName
        - storeResult
        - float1
        - float2
        - operation
        - storeResult
        - everyFrame
        - gameObject
        - fsmName
        - variableName
        - storeValue
        - everyFrame
        - float1
        - float2
        - operation
        - storeResult
        - everyFrame
        paramDataPos: 000000000000000000000000010000000100000000000000010000000200000002000000020000000300000003000000010000001b00000020000000240000003a0000000300000004000000050000003b00000055000000560000007000000075000000790000008f000000
        paramByteDataSize: 0000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000001a000000050000000400000016000000010000000000000000000000000000001a000000010000001a00000005000000040000001600000001000000
    - name: SETUP done?
      description: 
      colorIndex: 5
      position:
        serializedVersion: 2
        x: 64
        y: 112
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Drive Fsm done?
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetOwner
        - HutongGames.PlayMaker.Actions.FsmStateTest
        customNames:
        - 
        - 
        actionEnabled: 0101
        actionIsOpen: 0101
        actionStartIndex: 0000000001000000
        actionHashCodes: 5c00450505531600
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Owner_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: Owner_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: SETUP
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: -DONE-
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 46494e4953484544000101
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1300000013000000120000001200000017000000170000001100000001000000
        paramName:
        - storeGameObject
        - gameObject
        - fsmName
        - stateName
        - trueEvent
        - falseEvent
        - storeResult
        - everyFrame
        paramDataPos: 000000000100000000000000010000000000000008000000080000000a000000
        paramByteDataSize: 0000000000000000000000000000000008000000000000000200000001000000
    - name: Drive Fsm done?
      description: 
      colorIndex: 5
      position:
        serializedVersion: 2
        x: 208
        y: 128
        width: 124
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Steer Fsm done?
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.FsmStateTest
        - HutongGames.PlayMaker.Actions.FsmStateTest
        customNames:
        - 
        - 
        actionEnabled: 0101
        actionIsOpen: 0101
        actionStartIndex: 0000000007000000
        actionHashCodes: 0553160005531600
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Owner_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: Owner_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Movement/Drive
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: -Gas-
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Movement/Drive
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: -Brake-
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 46494e495348454400010146494e4953484544000101
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1300000012000000120000001700000017000000110000000100000013000000120000001200000017000000170000001100000001000000
        paramName:
        - gameObject
        - fsmName
        - stateName
        - trueEvent
        - falseEvent
        - storeResult
        - everyFrame
        - gameObject
        - fsmName
        - stateName
        - trueEvent
        - falseEvent
        - storeResult
        - everyFrame
        paramDataPos: 0000000000000000010000000000000008000000080000000a0000000100000002000000030000000b000000130000001300000015000000
        paramByteDataSize: 0000000000000000000000000800000000000000020000000100000000000000000000000000000008000000000000000200000001000000
    - name: Steer Fsm done?
      description: 
      colorIndex: 5
      position:
        serializedVersion: 2
        x: 368
        y: 144
        width: 122
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Wait for MinDistanceToWP
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.FsmStateTest
        - HutongGames.PlayMaker.Actions.FsmStateTest
        customNames:
        - 
        - 
        actionEnabled: 0101
        actionIsOpen: 0101
        actionStartIndex: 0000000007000000
        actionHashCodes: 0553160005531600
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: Owner_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: Owner_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Movement/Steer
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: -Steering-
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Movement/Steer
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: -NextFrame-
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 46494e495348454400010146494e4953484544000101
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1300000012000000120000001700000017000000110000000100000013000000120000001200000017000000170000001100000001000000
        paramName:
        - gameObject
        - fsmName
        - stateName
        - trueEvent
        - falseEvent
        - storeResult
        - everyFrame
        - gameObject
        - fsmName
        - stateName
        - trueEvent
        - falseEvent
        - storeResult
        - everyFrame
        paramDataPos: 0000000000000000010000000000000008000000080000000a0000000100000002000000030000000b000000130000001300000015000000
        paramByteDataSize: 0000000000000000000000000800000000000000020000000100000000000000000000000000000008000000000000000200000001000000
    - name: is Prefab near
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 608
        y: 480
        width: 105
        height: 48
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Is it visible
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      - fsmEvent:
          name: NO
          isSystemEvent: 0
          isGlobal: 0
        toState: 'Wait '
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetDistance
        - HutongGames.PlayMaker.Actions.FloatCompare
        customNames:
        - 
        - 
        actionEnabled: 0101
        actionIsOpen: 0101
        actionStartIndex: 0000000004000000
        actionHashCodes: 36f094001bcdca04
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: MainCam_g
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Prefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 000000000144697374616e6365546f43616d6572615f6600000000000144697374616e6365546f43616d6572615f66000070420000000000004e4f46494e49534845444e4f00
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 14000000130000000f000000010000000f0000000f0000000f00000017000000170000001700000001000000
        paramName:
        - gameObject
        - target
        - storeResult
        - everyFrame
        - float1
        - float2
        - tolerance
        - equal
        - lessThan
        - greaterThan
        - everyFrame
        paramDataPos: 00000000000000000000000017000000180000002f00000034000000390000003b0000004300000045000000
        paramByteDataSize: 0000000000000000170000000100000017000000050000000500000002000000080000000200000001000000
    - name: Set Kinematic mode
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 384
        y: 464
        width: 144
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: is Prefab near
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.SetIsKinematic
        - HutongGames.PlayMaker.Actions.SetVelocity
        - HutongGames.PlayMaker.Actions.ActivateGameObject
        - HutongGames.PlayMaker.Actions.SetFsmFloat
        - HutongGames.PlayMaker.Actions.SetFsmFloat
        - HutongGames.PlayMaker.Actions.SendEvent
        - HutongGames.PlayMaker.Actions.SendEvent
        customNames:
        - 
        - 
        - 
        - 
        - 
        - 
        - 
        actionEnabled: 01010101010101
        actionIsOpen: 01010101010101
        actionStartIndex: 0000000002000000090000000e00000013000000180000001c000000
        actionHashCodes: 15264c015552ad05b2cf100087604502876045025e69bb045e69bb04
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Prefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Prefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: PrefabWheelCollGroup_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams:
        - target: 2
          excludeSelf:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          gameObject:
            ownerOption: 0
            gameObject:
              useVariable: 0
              name: 
              tooltip: 
              showInInspector: 0
              networkSync: 0
              value: {fileID: 0}
          fsmName:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: Movement/Drive
          sendToChildren:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          fsmComponent: {fileID: 0}
        - target: 2
          excludeSelf:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          gameObject:
            ownerOption: 0
            gameObject:
              useVariable: 0
              name: 
              tooltip: 
              showInInspector: 0
              networkSync: 0
              value: {fileID: 0}
          fsmName:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: Movement/Steer
          sendToChildren:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          fsmComponent: {fileID: 0}
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Movement/Waypoints
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Distance_Min_toWaypoint_f
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Movement/Ray
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Distance_f
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 010000000000000000000000000001000000000000000000000000000000010000000000000000000000000000014d696e44697374546f57505f4e65775f6600000000000152617944697374616e63655f4e65775f6600472f4f4646000000000000472f4f4646000000000000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1400000011000000140000001c0000000f0000000f0000000f000000070000000100000014000000110000001100000001000000010000001400000012000000120000000f000000010000001400000012000000120000000f000000010000001f000000170000000f000000010000001f000000170000000f00000001000000
        paramName:
        - gameObject
        - isKinematic
        - gameObject
        - vector
        - x
        - y
        - z
        - space
        - everyFrame
        - gameObject
        - activate
        - recursive
        - resetOnExit
        - everyFrame
        - gameObject
        - fsmName
        - variableName
        - setValue
        - everyFrame
        - gameObject
        - fsmName
        - variableName
        - setValue
        - everyFrame
        - eventTarget
        - sendEvent
        - delay
        - everyFrame
        - eventTarget
        - sendEvent
        - delay
        - everyFrame
        paramDataPos: 000000000000000001000000020000000f00000014000000190000001e000000220000000200000023000000250000002700000028000000030000000000000001000000290000003f000000040000000200000003000000400000005600000000000000570000005c000000610000000100000062000000670000006c000000
        paramByteDataSize: 0000000002000000000000000d00000005000000050000000500000004000000010000000000000002000000020000000100000001000000000000000000000000000000160000000100000000000000000000000000000016000000010000000000000005000000050000000100000000000000050000000500000001000000
    - name: Kinematic mode off
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 384
        y: 400
        width: 143
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: is Prefab far or visible
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.SetIsKinematic
        - HutongGames.PlayMaker.Actions.ActivateGameObject
        - HutongGames.PlayMaker.Actions.SetFsmFloat
        - HutongGames.PlayMaker.Actions.SetFsmFloat
        - HutongGames.PlayMaker.Actions.SendEvent
        - HutongGames.PlayMaker.Actions.SendEvent
        customNames:
        - 
        - 
        - 
        - 
        - 
        - 
        actionEnabled: 010101010101
        actionIsOpen: 010101010101
        actionStartIndex: 0000000002000000070000000c0000001100000015000000
        actionHashCodes: 15264c01b2cf100087604502876045025e69bb045e69bb04
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Prefab_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: PrefabWheelCollGroup_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        - ownerOption: 0
          gameObject:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams:
        - target: 2
          excludeSelf:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          gameObject:
            ownerOption: 0
            gameObject:
              useVariable: 0
              name: 
              tooltip: 
              showInInspector: 0
              networkSync: 0
              value: {fileID: 0}
          fsmName:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: Movement/Drive
          sendToChildren:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          fsmComponent: {fileID: 0}
        - target: 2
          excludeSelf:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          gameObject:
            ownerOption: 0
            gameObject:
              useVariable: 0
              name: 
              tooltip: 
              showInInspector: 0
              networkSync: 0
              value: {fileID: 0}
          fsmName:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: Movement/Steer
          sendToChildren:
            useVariable: 0
            name: 
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: 0
          fsmComponent: {fileID: 0}
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Movement/Waypoints
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Distance_Min_toWaypoint_f
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Movement/Ray
        - useVariable: 0
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: Distance_f
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 000001000000000000000000014d696e44697374546f57505f44656661756c745f6600000000000152617944697374616e63655f44656661756c745f6600472f4f4e000000000000472f4f4e000000000000
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 140000001100000014000000110000001100000001000000010000001400000012000000120000000f000000010000001400000012000000120000000f000000010000001f000000170000000f000000010000001f000000170000000f00000001000000
        paramName:
        - gameObject
        - isKinematic
        - gameObject
        - activate
        - recursive
        - resetOnExit
        - everyFrame
        - gameObject
        - fsmName
        - variableName
        - setValue
        - everyFrame
        - gameObject
        - fsmName
        - variableName
        - setValue
        - everyFrame
        - eventTarget
        - sendEvent
        - delay
        - everyFrame
        - eventTarget
        - sendEvent
        - delay
        - everyFrame
        paramDataPos: 000000000000000001000000020000000400000006000000070000000200000000000000010000000800000022000000030000000200000003000000230000003d000000000000003e000000420000004700000001000000480000004c00000051000000
        paramByteDataSize: 000000000200000000000000020000000200000001000000010000000000000000000000000000001a000000010000000000000000000000000000001a000000010000000000000004000000050000000100000000000000040000000500000001000000
    - name: Wait for MinDistanceToWP
      description: 
      colorIndex: 5
      position:
        serializedVersion: 2
        x: 528
        y: 160
        width: 184
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: GET SET
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.FloatCompare
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: 1bcdca04
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 00000000014d696e44697374546f57505f44656661756c745f660000000000000000000046494e495348454400
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 0f0000000f0000000f00000017000000170000001700000001000000
        paramName:
        - float1
        - float2
        - tolerance
        - equal
        - lessThan
        - greaterThan
        - everyFrame
        paramDataPos: 000000001a0000001f0000002400000024000000240000002c000000
        paramByteDataSize: 1a000000050000000500000000000000000000000800000001000000
    - name: Wait
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 64
        y: 320
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: is Prefab far or visible
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.Wait
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: 48c01101
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 9a99993e0046494e495348454400
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 0f0000001700000001000000
        paramName:
        - time
        - finishEvent
        - realTime
        paramDataPos: 00000000050000000d000000
        paramByteDataSize: 050000000800000001000000
    - name: 'Wait '
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 800
        y: 480
        width: 100
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: is Prefab near
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.Wait
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: 48c01101
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 9a99993e0046494e495348454400
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 0f0000001700000001000000
        paramName:
        - time
        - finishEvent
        - realTime
        paramDataPos: 00000000050000000d000000
        paramByteDataSize: 050000000800000001000000
    - name: Is it visible
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 608
        y: 368
        width: 100
        height: 48
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: NO
          isSystemEvent: 0
          isGlobal: 0
        toState: NextFrame
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Kinematic mode off
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GameObjectIsVisible
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: 16e9ed03
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams:
        - ownerOption: 1
          gameObject:
            useVariable: 1
            name: Body_g
            tooltip: 
            showInInspector: 0
            networkSync: 0
            value: {fileID: 0}
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 46494e49534845444e4f000100
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 1400000017000000170000001100000001000000
        paramName:
        - gameObject
        - trueEvent
        - falseEvent
        - storeResult
        - everyFrame
        paramDataPos: 0000000000000000080000000a0000000c000000
        paramByteDataSize: 0000000008000000020000000200000001000000
    - name: NextFrame
      description: 'Here is NextFrame instead of Wait to catch the change asap.

'
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 800
        y: 368
        width: 239
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: is Prefab near
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.NextFrameEvent
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: 88611604
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 46494e4953484544
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 17000000
        paramName:
        - sendEvent
        paramDataPos: 00000000
        paramByteDataSize: 08000000
    events:
    - name: FINISHED
      isSystemEvent: 1
      isGlobal: 0
    - name: NO
      isSystemEvent: 0
      isGlobal: 0
    globalTransitions: []
    variables:
      floatVariables:
      - useVariable: 1
        name: DistanceToCamera_f
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      - useVariable: 1
        name: MinDistToWP_Default_f
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      - useVariable: 1
        name: MinDistToWP_New_f
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      - useVariable: 1
        name: RayDistance_Default_f
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      - useVariable: 1
        name: RayDistance_New_f
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      intVariables: []
      boolVariables: []
      stringVariables: []
      vector2Variables: []
      vector3Variables: []
      colorVariables: []
      rectVariables: []
      quaternionVariables: []
      gameObjectVariables:
      - useVariable: 1
        name: Body_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: MainCam_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: Owner_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: PrefabWheelCollGroup_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      - useVariable: 1
        name: Prefab_g
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: {fileID: 0}
      objectVariables: []
      materialVariables: []
      textureVariables: []
      arrayVariables: []
      enumVariables: []
      categories:
      - 
      variableCategoryIDs: 00000000000000000000000000000000000000000000000000000000000000000000000000000000
    description: 
    docUrl: 
    showStateLabel: 0
    maxLoopCount: 2000
    watermark: 
    password: 
    locked: 0
    manualUpdate: 0
    outVariableIndices: 
    keepDelayedEventsOnStateExit: 0
    preprocessed: 1
    ExposedEvents: []
    OutputEvents: []
    RestartOnEnable: 1
    ResetVariablesOnEnable: 0
    EnableDebugFlow: 0
    EnableBreakpoints: 1
    editorFlags: 278
    activeStateName: 
    mouseEvents: 0
    handleLevelLoaded: 0
    handleTriggerEnter2D: 0
    handleTriggerExit2D: 0
    handleTriggerStay2D: 0
    handleCollisionEnter2D: 0
    handleCollisionExit2D: 0
    handleCollisionStay2D: 0
    handleTriggerEnter: 0
    handleTriggerExit: 0
    handleTriggerStay: 0
    handleCollisionEnter: 0
    handleCollisionExit: 0
    handleCollisionStay: 0
    handleParticleCollision: 0
    handleControllerColliderHit: 0
    handleJointBreak: 0
    handleJointBreak2D: 0
    handleOnGUI: 0
    handleFixedUpdate: 1
    handleLateUpdate: 0
    handleApplicationEvents: 0
    handleUiEvents: 0
    handleLegacyNetworking: 0
    handleAnimatorMove: 0
    handleAnimatorIK: 0
  fsmTemplate: {fileID: 0}
  eventHandlerComponentsAdded: 1
--- !u!114 &11435798
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 108904}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1384774831, guid: de72a6d2da64d114d95e3c5a01cfaec5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fsm:
    dataVersion: 1
    usedInTemplate: {fileID: 0}
    name: LaneNR
    startState: ' '
    states:
    - name: ' '
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 64
        y: 112
        width: 100
        height: 16
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions: []
      actionData:
        actionNames: []
        customNames: []
        actionEnabled: 
        actionIsOpen: 
        actionStartIndex: 
        actionHashCodes: 
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 
        paramName: []
        paramDataPos: 
        paramByteDataSize: 
    - name: Get and wait
      description: 'A short wait so values dont overlap on Enter/Exit Trigger.

'
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 240
        y: 112
        width: 239
        height: 32
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions:
      - fsmEvent:
          name: FINISHED
          isSystemEvent: 1
          isGlobal: 0
        toState: Set Value
        linkStyle: 0
        linkConstraint: 0
        linkTarget: 0
        colorIndex: 0
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.GetEventInfo
        - HutongGames.PlayMaker.Actions.Wait
        customNames:
        - 
        - 
        actionEnabled: 0101
        actionIsOpen: 0101
        actionStartIndex: 000000000f000000
        actionHashCodes: d9ade60348c01101
        unityObjectParams: []
        fsmGameObjectParams:
        - useVariable: 1
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        - useVariable: 1
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: {fileID: 0}
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams:
        - useVariable: 1
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: 
        - useVariable: 1
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          value: 
        fsmObjectParams:
        - useVariable: 1
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          typeName: UnityEngine.Object
          value: {fileID: 0}
        - useVariable: 1
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          typeName: UnityEngine.Object
          value: {fileID: 0}
        - useVariable: 1
          name: 
          tooltip: 
          showInInspector: 0
          networkSync: 0
          typeName: UnityEngine.Object
          value: {fileID: 0}
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 0001000000000156616c75655f69000000000100000000000000000100000000000000000000000001000000000000000000000000000000000100000000000000000000000000000000010000000000000000000000000000803f010000803f0046494e495348454401
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 130000001200000011000000100000000f000000250000001c00000012000000130000001e00000023000000200000002100000019000000180000000f0000001700000001000000
        paramName:
        - sentByGameObject
        - fsmName
        - getBoolData
        - getIntData
        - getFloatData
        - getVector2Data
        - getVector3Data
        - getStringData
        - getGameObjectData
        - getRectData
        - getQuaternionData
        - getMaterialData
        - getTextureData
        - getColorData
        - getObjectData
        - time
        - finishEvent
        - realTime
        paramDataPos: 000000000000000000000000020000000e000000130000001c0000000100000001000000290000003a00000000000000010000004b000000020000005c0000006100000069000000
        paramByteDataSize: 0000000000000000020000000c00000005000000090000000d0000000000000000000000110000001100000000000000000000001100000000000000050000000800000001000000
    - name: Set Value
      description: 
      colorIndex: 0
      position:
        serializedVersion: 2
        x: 320
        y: 208
        width: 100
        height: 16
      isBreakpoint: 0
      isSequence: 0
      hideUnused: 0
      transitions: []
      actionData:
        actionNames:
        - HutongGames.PlayMaker.Actions.SetIntValue
        customNames:
        - 
        actionEnabled: 01
        actionIsOpen: 01
        actionStartIndex: 00000000
        actionHashCodes: 603dd905
        unityObjectParams: []
        fsmGameObjectParams: []
        fsmOwnerDefaultParams: []
        animationCurveParams: []
        functionCallParams: []
        fsmTemplateControlParams: []
        fsmEventTargetParams: []
        fsmPropertyParams: []
        layoutOptionParams: []
        fsmStringParams: []
        fsmObjectParams: []
        fsmVarParams: []
        fsmArrayParams: []
        fsmEnumParams: []
        fsmFloatParams: []
        fsmIntParams: []
        fsmBoolParams: []
        fsmVector2Params: []
        fsmVector3Params: []
        fsmColorParams: []
        fsmRectParams: []
        fsmQuaternionParams: []
        stringParams: []
        byteData: 00000000014c616e655f557365645f69000000000156616c75655f6900
        arrayParamSizes: 
        arrayParamTypes: []
        customTypeSizes: 
        customTypeNames: []
        paramDataType: 100000001000000001000000
        paramName:
        - intVariable
        - intValue
        - everyFrame
        paramDataPos: 00000000100000001c000000
        paramByteDataSize: 100000000c00000001000000
    events:
    - name: FINISHED
      isSystemEvent: 1
      isGlobal: 0
    - name: G/ON
      isSystemEvent: 0
      isGlobal: 1
    globalTransitions:
    - fsmEvent:
        name: G/ON
        isSystemEvent: 0
        isGlobal: 1
      toState: Get and wait
      linkStyle: 0
      linkConstraint: 0
      linkTarget: 0
      colorIndex: 0
    variables:
      floatVariables: []
      intVariables:
      - useVariable: 1
        name: Lane_Used_i
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      - useVariable: 1
        name: Value_i
        tooltip: 
        showInInspector: 0
        networkSync: 0
        value: 0
      boolVariables: []
      stringVariables: []
      vector2Variables: []
      vector3Variables: []
      colorVariables: []
      rectVariables: []
      quaternionVariables: []
      gameObjectVariables: []
      objectVariables: []
      materialVariables: []
      textureVariables: []
      arrayVariables: []
      enumVariables: []
      categories:
      - 
      variableCategoryIDs: 0000000000000000
    description: 
    docUrl: 
    showStateLabel: 0
    maxLoopCount: 0
    watermark: 
    password: 
    locked: 0
    manualUpdate: 0
    outVariableIndices: 
    keepDelayedEventsOnStateExit: 0
    preprocessed: 1
    ExposedEvents: []
    OutputEvents: []
    RestartOnEnable: 1
    ResetVariablesOnEnable: 0
    EnableDebugFlow: 0
    EnableBreakpoints: 1
    editorFlags: 2
    activeStateName: 
    mouseEvents: 0
    handleLevelLoaded: 0
    handleTriggerEnter2D: 0
    handleTriggerExit2D: 0
    handleTriggerStay2D: 0
    handleCollisionEnter2D: 0
    handleCollisionExit2D: 0
    handleCollisionStay2D: 0
    handleTriggerEnter: 0
    handleTriggerExit: 0
    handleTriggerStay: 0
    handleCollisionEnter: 0
    handleCollisionExit: 0
    handleCollisionStay: 0
    handleParticleCollision: 0
    handleControllerColliderHit: 0
    handleJointBreak: 0
    handleJointBreak2D: 0
    handleOnGUI: 0
    handleFixedUpdate: 0
    handleLateUpdate: 0
    handleApplicationEvents: 0
    handleUiEvents: 0
    handleLegacyNetworking: 0
    handleAnimatorMove: 0
    handleAnimatorIK: 0
  fsmTemplate: {fileID: 0}
  eventHandlerComponentsAdded: 1
--- !u!114 &3029714609040087604
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 108904}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1079068393, guid: e743331561ef77147ae48cda9bcb8209, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TargetFSMs:
  - {fileID: 11455448}
  - {fileID: 11446606}
--- !u!114 &2781451303950235560
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 108904}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -664232604, guid: e743331561ef77147ae48cda9bcb8209, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TargetFSMs:
  - {fileID: 11401864}
