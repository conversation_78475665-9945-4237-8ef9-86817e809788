%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 42938266, guid: e743331561ef77147ae48cda9bcb8209, type: 3}
  m_Name: PlayMakerGlobals
  m_EditorClassIdentifier: 
  variables:
    floatVariables: []
    intVariables: []
    boolVariables: []
    stringVariables: []
    vector2Variables: []
    vector3Variables: []
    colorVariables: []
    rectVariables: []
    quaternionVariables: []
    gameObjectVariables: []
    objectVariables: []
    materialVariables: []
    textureVariables: []
    arrayVariables: []
    enumVariables: []
    categories:
    - 
    variableCategoryIDs: 
  events:
  - YES
  - G/ON
  - G/OFF
  - UGUI / ON BEGIN DRAG
  - UGUI / ON BOOL VALUE CHANGED
  - UGUI / ON CLICK
  - UGUI / ON DRAG
  - UGUI / ON DROP
  - UGUI / ON END DRAG
  - UGUI / ON END EDIT
  - UGUI / ON FLOAT VALUE CHANGED
  - UGUI / ON INT VALUE CHANGED
  - UGUI / ON POINTER CLICK
  - UGUI / ON POINTER DOWN
  - UGUI / ON POINTER ENTER
  - UGUI / ON POINTER EXIT
  - UGUI / ON POINTER UP
  - UGUI / ON VECTOR2 VALUE CHANGED
  - CAM/Free
  - CAM/Ped
  - CAM/Car
  - CAR_CREATION_COMPLETE
  - PED_CREATION_COMPLETE
  - CAM/Delivery
